/**
 * Handles AI response processing and delivery
 * @module aiResponseHandler
 */

import { getVectorStoreIndex } from '../../vectorStore.js';
import { generateEmbedding, callFastGenerativeAI } from '../geminiAI.js';
import { logTelegramMessage } from '../../redis.js';
import { formatTimestamp } from '../utils/formatUtils.js';
import {
	SUMMARY_TEMPERATURE,
	SUMMARY_SYSTEM_PROMPT,
	SUMMARY_PROMPT,
	REFINE_RESPONSE_TEMPERATURE,
	REFINE_RESPONSE_SYSTEM_PROMPT,
	REFINE_RESPONSE,
} from '../../constants/index.js';
import { generateSuggestions } from './suggestionGenerator.js';

/**
 * Handles logging, escaping, and sending the AI response back to Telegram
 * @param {Object} env - Environment variables
 * @param {string|number} chatId - The chat ID
 * @param {string|Array} aiResponseText - The AI response text (can be an array of responses)
 * @param {string|number} originalMessageId - The ID of the original message being responded to
 * @param {string} originalMessageText - The original user message text.
 * @param {string} userFacts - The user's extracted facts.
 * @param {string} conversationHistory - The recent conversation history.
 * @param {Array} [aiResponseThoughts] - Optional array of AI thoughts
 * @returns {Promise<void>}
 */
export async function handle(
	env,
	chatId,
	aiResponseText,
	originalMessageId,
	originalMessageText,
	userFacts,
	conversationHistory,
	aiResponseThoughts = []
) {
	// Import here to avoid circular dependencies
	const { escapeHtml } = await import('../telegramUtils.js');
	const { sendLongTelegramMessage } = await import('../../telegram.js');

	try {
		// Handle array of responses: use only the last response if array
		let responseText = _extractResponseText(aiResponseText);

		// Refine the AI response if enabled
		if (REFINE_RESPONSE) {
			const refineConfig = {
				temperature: REFINE_RESPONSE_TEMPERATURE,
				systemInstruction: REFINE_RESPONSE_SYSTEM_PROMPT,
				inferenceProvider: 'groq',
				model: 'moonshotai/kimi-k2-instruct',
				traceTags: ['response-refine'],
			};

			const refineContents = [
				{
					role: 'user',
					parts: [{ text: responseText }],
				},
			];

			const refinedResponse = await callFastGenerativeAI(env, refineConfig, refineContents);
			responseText = refinedResponse.text;
		}

		// Log thoughts if present
		if (aiResponseThoughts?.length > 0) {
			console.log('AI Thoughts:', aiResponseThoughts);
		}

		// Log and store the response
		await _logAndStoreResponse(env, chatId, responseText, originalMessageId, aiResponseThoughts);

		const suggestions = await generateSuggestions(
			env,
			originalMessageText,
			responseText,
			userFacts,
			conversationHistory,
			formatTimestamp(Date.now() / 1000, env, 'date'),
			formatTimestamp(Date.now() / 1000, env, 'time')
		);

		// Send the final response to Telegram with suggestions
		await _sendResponse(env, chatId, responseText, originalMessageId, sendLongTelegramMessage, escapeHtml, suggestions);
	} catch (error) {
		console.error('Error in handleAIResponse:', error);
		throw error;
	}
}

/**
 * Extracts response text from possible response formats
 * @private
 * @param {string|Array|Object} response - The AI response
 * @returns {string} Extracted text
 */
function _extractResponseText(response) {
	if (!response) return '';

	if (Array.isArray(response)) {
		const last = response[response.length - 1];
		return last?.text.trim() || '';
	}

	return typeof response === 'string' ? response.trim() : '';
}

/**
 * Logs the AI response to Redis and stores its embedding
 * @private
 * @param {Object} env - Environment variables
 * @param {string|number} chatId - The chat ID
 * @param {string} responseText - The response text to log and store
 * @param {string|number} originalMessageId - The original message ID
 * @param {Array} thoughts - AI thoughts
 * @returns {Promise<void>}
 */
async function _logAndStoreResponse(env, chatId, responseText, originalMessageId, thoughts = []) {
	const aiMessageTimestamp = Date.now() / 1000;

	// Generate summary first so we can store it in both Redis and vector DB
	const summary = responseText ? await _generateResponseSummary(env, responseText) : '';

	// Log to Redis with summary
	logTelegramMessage(env, chatId, {
		text: responseText,
		summary: summary,
		thoughts,
		from: {
			username: env.TELEGRAM_BOT_NAME,
			is_bot: true,
		},
		date: aiMessageTimestamp,
	});

	// Store embedding if response is not empty
	if (!responseText) return;

	try {
		const vectorIndex = getVectorStoreIndex(env);
		const aiMessageEmbedding = await generateEmbedding(env, responseText);

		if (!aiMessageEmbedding) {
			console.warn('Could not generate embedding for AI response.');
			return;
		}

		const aiMessageId = `${originalMessageId}_ai_response`;
		await vectorIndex.upsert({
			id: aiMessageId,
			vector: aiMessageEmbedding,
			metadata: {
				text: responseText,
				summary: summary,
				chatId,
				userId: null, // Or env.TELEGRAM_BOT_ID if available
				role: 'assistant',
				timestamp: aiMessageTimestamp,
				replyToMessageId: originalMessageId.toString(),
			},
		});

		console.log(`AI response for message ${originalMessageId} embedded and stored with summary in both Redis and vector DB.`);
	} catch (error) {
		console.error('Error storing AI response embedding:', error);
		// Continue even if embedding fails
	}
}

/**
 * Generates a concise summary of the AI response using fast AI
 * @private
 * @param {Object} env - Environment variables
 * @param {string} responseText - The response text to summarize
 * @returns {Promise<string>} Generated summary or empty string if failed
 */
async function _generateResponseSummary(env, responseText) {
	try {
		const config = {
			temperature: SUMMARY_TEMPERATURE,
			systemInstruction: SUMMARY_SYSTEM_PROMPT,
			inferenceProvider: 'cerebras',
			model: 'qwen-3-235b-a22b-instruct-2507',
			traceTags: ['response-summary'],
		};

		const contents = [
			{
				role: 'user',
				parts: [
					{
						text: SUMMARY_PROMPT.replace('{AI_RESPONSE}', responseText),
					},
				],
			},
		];

		const response = await callFastGenerativeAI(env, config, contents);
		console.log('AI Response Summary:', response);

		if (response && response.text) {
			// Clean and truncate the summary
			const summary = response.text.trim().replace(/\n/g, ' ');
			return summary;
		}

		// Fallback to simple truncation if AI summary fails
		return responseText.substring(0, 100) + (responseText.length > 100 ? '...' : '');
	} catch (error) {
		console.warn('Failed to generate AI response summary:', error.message);
		// Fallback to simple truncation
		return responseText.substring(0, 100) + (responseText.length > 100 ? '...' : '');
	}
}

/**
 * Sends the AI response to the user
 * @private
 * @param {Object} env - Environment variables
 * @param {string|number} chatId - The chat ID
 * @param {string} responseText - The response text to send
 * @param {string|number} originalMessageId - The original message ID
 * @param {Function} sendMessage - Function to send the message
 * @param {Function} escapeFn - Function to escape and format text (HTML or Markdown)
 * @returns {Promise<void>}
 */
async function _sendResponse(env, chatId, responseText, originalMessageId, sendMessage, escapeFn, suggestions = []) {
	if (!responseText) {
		console.warn('Empty response text, nothing to send');
		return;
	}

	console.log('Original AI Response:', responseText);
	const formattedResponse = escapeFn(responseText);
	console.log('Formatted AI Response:', formattedResponse);

	try {
		const messageOptions = { message_id: originalMessageId, parseMode: 'HTML' };
		const sendResult = await sendMessage(env, chatId, formattedResponse, messageOptions, suggestions);

		if (sendResult) {
			console.log(`Successfully sent AI response to chat ${chatId}`);
		} else {
			console.error(`Failed to send AI response to chat ${chatId}`);
		}
	} catch (error) {
		console.error(`Error sending AI response to chat ${chatId}:`, error);
		throw error;
	}
}
