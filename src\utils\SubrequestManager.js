/**
 * @fileoverview Manages and monitors subrequests to prevent hitting Cloudflare Workers limits
 */

/**
 * Subrequest manager to track and limit outgoing requests
 */
export class SubrequestManager {
	constructor(maxSubrequests = 45) { // Leave buffer below the 50 limit
		this.maxSubrequests = maxSubrequests;
		this.subrequestCount = 0;
		this.pendingRequests = new Set();
		this.requestQueue = [];
		this.isProcessingQueue = false;
	}

	/**
	 * Get current subrequest count
	 * @returns {number} Current number of subrequests made
	 */
	getCount() {
		return this.subrequestCount;
	}

	/**
	 * Get remaining subrequest capacity
	 * @returns {number} Number of subrequests remaining
	 */
	getRemaining() {
		return Math.max(0, this.maxSubrequests - this.subrequestCount);
	}

	/**
	 * Check if we can make more subrequests
	 * @returns {boolean} True if we can make more requests
	 */
	canMakeRequest() {
		return this.subrequestCount < this.maxSubrequests;
	}

	/**
	 * Increment subrequest counter
	 * @private
	 */
	_incrementCount() {
		this.subrequestCount++;
		console.log(`[SubrequestManager] Count: ${this.subrequestCount}/${this.maxSubrequests}`);
	}

	/**
	 * Decrement subrequest counter
	 * @private
	 */
	_decrementCount() {
		this.subrequestCount = Math.max(0, this.subrequestCount - 1);
	}

	/**
	 * Wrap fetch to track subrequests
	 * @param {RequestInfo | URL} input - Request input
	 * @param {RequestInit} init - Request options
	 * @returns {Promise<Response>} Fetch response
	 */
	async fetch(input, init = {}) {
		if (!this.canMakeRequest()) {
			throw new Error(`Subrequest limit reached (${this.maxSubrequests}). Cannot make more requests.`);
		}

		this._incrementCount();
		const requestId = Math.random().toString(36).substr(2, 9);
		this.pendingRequests.add(requestId);

		try {
			const response = await fetch(input, init);
			return response;
		} finally {
			this.pendingRequests.delete(requestId);
			this._decrementCount();
		}
	}

	/**
	 * Queue a request to be executed when capacity is available
	 * @param {Function} requestFn - Function that returns a Promise for the request
	 * @returns {Promise} Promise that resolves when the request is executed
	 */
	async queueRequest(requestFn) {
		return new Promise((resolve, reject) => {
			this.requestQueue.push({ requestFn, resolve, reject });
			this._processQueue();
		});
	}

	/**
	 * Process queued requests
	 * @private
	 */
	async _processQueue() {
		if (this.isProcessingQueue || this.requestQueue.length === 0) {
			return;
		}

		this.isProcessingQueue = true;

		while (this.requestQueue.length > 0 && this.canMakeRequest()) {
			const { requestFn, resolve, reject } = this.requestQueue.shift();

			try {
				const result = await requestFn();
				resolve(result);
			} catch (error) {
				reject(error);
			}
		}

		this.isProcessingQueue = false;
	}

	/**
	 * Execute multiple requests with concurrency limit
	 * @param {Array<Function>} requestFunctions - Array of functions that return Promises
	 * @param {number} concurrency - Maximum concurrent requests (default: 3)
	 * @returns {Promise<Array>} Array of results
	 */
	async executeWithConcurrency(requestFunctions, concurrency = 3) {
		const results = [];
		const executing = [];

		for (const requestFn of requestFunctions) {
			if (!this.canMakeRequest()) {
				console.warn('[SubrequestManager] Skipping request due to limit');
				results.push(null);
				continue;
			}

			const promise = this.queueRequest(requestFn).then(result => {
				executing.splice(executing.indexOf(promise), 1);
				return result;
			});

			results.push(promise);
			executing.push(promise);

			if (executing.length >= concurrency) {
				await Promise.race(executing);
			}
		}

		return Promise.all(results);
	}

	/**
	 * Get status information
	 * @returns {object} Status object with counts and limits
	 */
	getStatus() {
		return {
			count: this.subrequestCount,
			max: this.maxSubrequests,
			remaining: this.getRemaining(),
			pending: this.pendingRequests.size,
			queued: this.requestQueue.length,
			canMakeRequest: this.canMakeRequest()
		};
	}

	/**
	 * Reset the counter (use with caution)
	 */
	reset() {
		this.subrequestCount = 0;
		this.pendingRequests.clear();
		this.requestQueue = [];
		this.isProcessingQueue = false;
		console.log('[SubrequestManager] Reset counter');
	}
}

// Global instance
let globalSubrequestManager = null;

/**
 * Get or create global subrequest manager instance
 * @param {number} maxSubrequests - Maximum subrequests allowed
 * @returns {SubrequestManager} Global subrequest manager instance
 */
export function getSubrequestManager(maxSubrequests = 45) {
	if (!globalSubrequestManager) {
		globalSubrequestManager = new SubrequestManager(maxSubrequests);
	}
	return globalSubrequestManager;
}

/**
 * Reset global subrequest manager
 */
export function resetSubrequestManager() {
	globalSubrequestManager = null;
}
