import { deleteChatHistory } from '../redis.js';
import { deleteChatEmbeddings } from '../vectorStore.js';
import { sendLongTelegramMessage } from '../telegram.js';
import { escapeHtml } from './telegramUtils.js';
import { TaskManager } from './tasks/taskManager.js';
import { getRedisClient } from '../redis/redisClient.js';

/**
 * Command configuration object that maps command types to their trigger strings.
 * This allows for flexible command handling with multiple aliases for the same command.
 *
 * @type {Object<string, string[]>}
 * @property {string[]} CLEAR_HISTORY - Array of command strings that trigger history clearing
 *
 * @example
 * // Adding a new command with multiple aliases:
 * const COMMANDS = {
 *   CLEAR_HISTORY: ['/clear', '/reset', '/start-over'],
 *   HELP: ['/help', '/info']
 * };
 */
const COMMANDS = {
	// Command for clearing chat history with all associated data
	CLEAR_HISTORY: ['/clear'],
	// Commands for task management
	LIST_TASKS: ['/tasks'],
	COMPLETE_TASK: ['/done'],
	DELETE_TASK: ['/delete_task'],
};

/**
 * Detects if a given text contains a valid command.
 *
 * This function checks if the input text matches any registered command exactly
 * or if it starts with a command followed by a space (allowing for command arguments).
 * The comparison is case-insensitive.
 *
 * @param {string|null|undefined} text - The text to check for commands
 * @returns {string|null} The command type if found (e.g., 'CLEAR_HISTORY'), null otherwise
 *
 * @example
 * detectCommand('/clear') // returns 'CLEAR_HISTORY'
 * detectCommand('/clear some arguments') // returns 'CLEAR_HISTORY'
 * detectCommand('/unknown') // returns null
 * detectCommand('') // returns null
 */
export function detectCommand(text) {
	// Validate input - ensure text is a non-empty string
	if (!text || typeof text !== 'string') {
		return null;
	}

	// Convert to lowercase once for efficient comparison
	// This optimization avoids repeated case conversion in loops
	const trimmedText = text.toLowerCase().trim();

	// Check each command type and its associated command strings
	for (const commandType in COMMANDS) {
		for (const command of COMMANDS[commandType]) {
			const lowerCaseCommand = command.toLowerCase();

			// Match exact command or command followed by space (with arguments)
			// This allows commands to have additional parameters
			if (trimmedText === lowerCaseCommand || trimmedText.startsWith(lowerCaseCommand + ' ')) {
				return commandType;
			}
		}
	}

	// No command detected
	return null;
}

/**
 * Handles the clear history command by deleting all chat-related data.
 *
 * This function performs a comprehensive cleanup of a chat's data by:
 * 1. Removing chat history from Redis storage
 * 2. Deleting chat embeddings from the vector store
 *
 * The function implements a robust error handling strategy where each deletion
 * operation is attempted independently. Even if one operation fails, others
 * will still be attempted, and the user receives detailed feedback about
 * the success status of each operation.
 *
 * @param {Object} env - Environment variables containing configuration and secrets
 * @param {string} chatId - The ID of the chat whose history should be cleared
 * @param {string} userId - The ID of the user requesting the action
 * @param {string} botUsername - The username of the bot (used for Redis key construction)
 * @returns {Promise<boolean>} True if the command was processed successfully, false otherwise
 *
 * @example
 * const success = await handleClearHistoryCommand(env, '12345', '67890', 'mybot');
 */
/**
 * Extracts the argument from a command string.
 * @param {string} text - The full command text (e.g., "/done buy milk").
 * @param {string} command - The command itself (e.g., "/done").
 * @returns {string} The argument, or an empty string if not present.
 */
function getCommandArgument(text, command) {
	return text.substring(command.length).trim();
}

async function handleListTasksCommand(env, chatId, userId) {
	try {
		const redisClient = getRedisClient(env);
		const taskManager = new TaskManager(redisClient);
		const tasks = await taskManager.getTasks(userId);
		const pendingTasks = tasks.filter((task) => task.status === 'pending');

		let responseMessage;
		if (pendingTasks.length === 0) {
			responseMessage = 'You have no pending tasks! ✨';
		} else {
			const taskList = pendingTasks.map((task, index) => `${index + 1}. ${escapeHtml(task.description)}`).join('\n');
			responseMessage = `<b>Your Pending Tasks:</b>\n${taskList}`;
		}

		await sendLongTelegramMessage(env, chatId, responseMessage, { parseMode: 'HTML' });
		return true;
	} catch (error) {
		console.error(`Error handling /tasks command for chat ${chatId}:`, error);
		await sendLongTelegramMessage(env, chatId, "❌ Sorry, I couldn't retrieve your tasks right now.", { parseMode: 'HTML' });
		return false;
	}
}

async function handleCompleteTaskCommand(env, chatId, userId, text) {
	try {
		const taskDescription = getCommandArgument(text, '/done');
		if (!taskDescription) {
			await sendLongTelegramMessage(env, chatId, 'Please specify which task to complete. Usage: `/done [task description]`');
			return false;
		}

		const redisClient = getRedisClient(env);
		const taskManager = new TaskManager(redisClient);
		const completedTask = await taskManager.completeTaskByDescription(userId, taskDescription);

		let responseMessage;
		if (completedTask) {
			responseMessage = `✅ Great job! I've marked "${escapeHtml(completedTask.description)}" as completed.`;
		} else {
			responseMessage = `🤔 I couldn't find a pending task matching "${escapeHtml(taskDescription)}".`;
		}

		await sendLongTelegramMessage(env, chatId, responseMessage, { parseMode: 'HTML' });
		return true;
	} catch (error) {
		console.error(`Error handling /done command for chat ${chatId}:`, error);
		await sendLongTelegramMessage(env, chatId, "❌ Sorry, I couldn't complete that task right now.", { parseMode: 'HTML' });
		return false;
	}
}

async function handleDeleteTaskCommand(env, chatId, userId, text) {
	try {
		const taskDescription = getCommandArgument(text, '/delete_task');
		if (!taskDescription) {
			await sendLongTelegramMessage(env, chatId, 'Please specify which task to delete. Usage: `/delete_task [task description]`');
			return false;
		}

		const redisClient = getRedisClient(env);
		const taskManager = new TaskManager(redisClient);
		const success = await taskManager.deleteTaskByDescription(userId, taskDescription);

		let responseMessage;
		if (success) {
			responseMessage = `🗑️ Okay, I've deleted the task matching "${escapeHtml(taskDescription)}".`;
		} else {
			responseMessage = `🤔 I couldn't find a pending task matching "${escapeHtml(taskDescription)}".`;
		}

		await sendLongTelegramMessage(env, chatId, responseMessage, { parseMode: 'HTML' });
		return true;
	} catch (error) {
		console.error(`Error handling /delete_task command for chat ${chatId}:`, error);
		await sendLongTelegramMessage(env, chatId, "❌ Sorry, I couldn't delete that task right now.", { parseMode: 'HTML' });
		return false;
	}
}

async function handleClearHistoryCommand(env, chatId, userId, botUsername) {
	try {
		console.log(`[handleClearHistoryCommand] Processing clear history command for chat ${chatId}, user ${userId}`);
		console.log(`[handleClearHistoryCommand] Bot username: "${botUsername}"`);
		console.log(`[handleClearHistoryCommand] Environment bot username: "${env.TELEGRAM_BOT_USERNAME}"`);

		// Object to track the success status of each deletion operation
		// This allows for independent error handling and detailed user feedback
		const deletionResults = {
			// Whether Redis chat history was successfully deleted
			redis: false,
			// Whether vector store embeddings were successfully deleted
			// This operation may take longer for chats with extensive history
			embeddings: false,
		};

		// Delete chat history from Redis storage
		// This removes the conversation messages and metadata
		try {
			console.log(`[handleClearHistoryCommand] Starting Redis chat history deletion...`);
			// The deleteChatHistory function returns a boolean indicating success
			deletionResults.redis = await deleteChatHistory(env, chatId, botUsername);
			console.log(`[handleClearHistoryCommand] Redis chat history deletion result: ${deletionResults.redis}`);
		} catch (error) {
			// Log the error for debugging but continue with other operations
			// This ensures that a failure in one storage system doesn't prevent
			// cleanup in other systems
			console.error(`[handleClearHistoryCommand] Error deleting Redis chat history for chat ${chatId}:`, error);
			deletionResults.redis = false;
		}

		// Delete chat embeddings from the vector store
		// This removes the AI-processed representations of the chat history
		// which are used for semantic search and context retrieval
		// Note: This operation may take longer for chats with extensive history
		// as it involves potentially multiple database operations
		try {
			console.log(`[handleClearHistoryCommand] Starting vector store cleanup for chat ${chatId}...`);
			// The deleteChatEmbeddings function returns a boolean indicating success
			deletionResults.embeddings = await deleteChatEmbeddings(env, chatId);
			console.log(`[handleClearHistoryCommand] Vector store cleanup completed for chat ${chatId}. Result: ${deletionResults.embeddings}`);
		} catch (error) {
			// Log the error for debugging but continue execution
			// This follows the principle of graceful degradation -
			// even if one data store fails, we attempt to clean up others
			console.error(`[handleClearHistoryCommand] Error deleting chat embeddings for chat ${chatId}:`, error);
			deletionResults.embeddings = false;
		}

		// Prepare user-facing response message based on operation results
		// The response provides detailed feedback about what was successfully
		// deleted and what might have failed
		let responseMessage;

		// Check if all deletion operations were successful
		const allSuccessful = deletionResults.redis && deletionResults.embeddings;
		// Check if at least one deletion operation was successful
		const anySuccessful = deletionResults.redis || deletionResults.embeddings;

		// Count successful operations to provide detailed feedback
		const successCount = Object.values(deletionResults).filter((result) => result === true).length;
		const totalOperations = Object.keys(deletionResults).length;

		if (allSuccessful) {
			// All data stores were successfully cleared
			responseMessage = `✅ All data cleared successfully! We can start completely fresh! (${successCount}/${totalOperations} operations completed)`;
		} else if (anySuccessful) {
			// Some data was cleared but not all - provide guidance for next steps
			responseMessage = `⚠️ Partial data cleared. ${successCount}/${totalOperations} operations completed successfully. Some items could not be deleted. Try again or contact support.`;
		} else {
			// All deletion operations failed - suggest retrying
			responseMessage = `❌ Failed to clear any data. Please try again later.`;
		}

		// Send the response message to the user via Telegram
		// The message is formatted for HTML parsing
		const formattedResponse = escapeHtml(responseMessage);
		await sendLongTelegramMessage(env, chatId, formattedResponse, { parseMode: 'HTML' });

		// Log completion with detailed results for monitoring and debugging
		console.log(`Clear history command completed for chat ${chatId}. Results:`, deletionResults);
		return true;
	} catch (error) {
		// Handle any unexpected errors that occurred during command processing
		console.error(`Error handling clear history command for chat ${chatId}:`, error);

		// Send user-friendly error message to the user
		// This ensures the user is informed even if something goes wrong
		const errorMessage = `❌ Error clearing chat history. Please try again later.`;

		try {
			// Attempt to send the error message
			const formattedErrorResponse = escapeHtml(errorMessage);
			await sendLongTelegramMessage(env, chatId, formattedErrorResponse, { parseMode: 'HTML' });
		} catch (sendError) {
			// If we can't even send the error message, log it for debugging
			console.error(`Failed to send error message for clear history command:`, sendError);
		}

		// Return false to indicate command processing failed
		return false;
	}
}

/**
 * Processes a detected command by routing it to the appropriate handler function.
 *
 * This function acts as a command router, mapping command types to their
 * corresponding handler functions. It follows the Open/Closed Principle by
 * allowing new commands to be added without modifying existing code.
 *
 * @param {string} commandType - The type of command to process (e.g., 'CLEAR_HISTORY')
 * @param {Object} env - Environment variables
 * @param {Object} messageData - The message data containing chat and user information
 * @param {string} botUsername - The bot's username for context
 * @returns {Promise<boolean>} True if the command was processed successfully, false otherwise
 *
 * @example
 * await processCommand('CLEAR_HISTORY', env, messageData, 'mybot');
 */
export async function processCommand(commandType, env, messageData, botUsername, text) {
	const { chatId, userId } = messageData;

	// Route the command to the appropriate handler function
	switch (commandType) {
		case 'CLEAR_HISTORY':
			return await handleClearHistoryCommand(env, chatId, userId, botUsername);
		case 'LIST_TASKS':
			return await handleListTasksCommand(env, chatId, userId);
		case 'COMPLETE_TASK':
			return await handleCompleteTaskCommand(env, chatId, userId, text);
		case 'DELETE_TASK':
			return await handleDeleteTaskCommand(env, chatId, userId, text);
		default:
			console.warn(`Unknown command type: ${commandType}`);
			return false;
	}
}

/**
 * Main entry point for command handling.
 *
 * This function orchestrates the entire command processing flow:
 * 1. Detects if the input text contains a command
 * 2. If a command is detected, processes it using the appropriate handler
 * 3. Returns the result of command processing
 *
 * @param {string} text - The text to check for commands
 * @param {Object} env - Environment variables
 * @param {Object} messageData - The message data containing chat and user information
 * @param {string} botUsername - The bot's username for context
 * @returns {Promise<boolean>} True if a command was detected and processed, false otherwise
 *
 * @example
 * const handled = await handleCommand('/clear', env, messageData, 'mybot');
 * // Returns true if command was processed, false if no command was detected
 */
export async function handleCommand(text, env, messageData, botUsername) {
	const commandType = detectCommand(text);

	if (!commandType) {
		return false;
	}

	console.log(`Command detected: ${commandType} from user ${messageData.userId} in chat ${messageData.chatId}`);

	// Pass the full text to processCommand to handle arguments
	return await processCommand(commandType, env, messageData, botUsername, text);
}
