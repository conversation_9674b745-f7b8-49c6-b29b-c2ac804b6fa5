{"name": "harmony-chat", "version": "1.0.0", "private": true, "scripts": {"deploy": "wrangler deploy --env production", "dev": "wrangler dev", "dev:public": "wrangler dev --remote"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250628.0", "@eslint/js": "^9.29.0", "eslint": "^9.29.0", "globals": "^16.2.0", "wrangler": "^4.21.2"}, "dependencies": {"@langchain/community": "^0.3.50", "@langchain/core": "^0.3.70", "@langchain/exa": "^0.1.0", "@langchain/google-common": "^0.2.12", "@langchain/google-genai": "^0.2.12", "@langchain/langgraph": "^0.3.3", "@langchain/openai": "^0.5.13", "@upstash/redis": "^1.35.0", "@upstash/vector": "^1.2.1", "hono": "^4.7.10", "langchain": "^0.3.28", "langfuse-langchain": "^3.37.6", "nanoid": "^5.1.5"}}