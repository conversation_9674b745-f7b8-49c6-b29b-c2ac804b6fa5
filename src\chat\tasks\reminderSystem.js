/**
 * Calculate next reminder time based on task and user preferences
 *
 * @param {Object} options - Task options containing reminder frequency settings
 * @param {string} [lastReminderAt=null] - ISO timestamp of last reminder
 * @returns {Date} The calculated next reminder time
 */
export function calculateNextReminder(options, lastReminderAt = null) {
	const now = new Date();
	const baseTime = lastReminderAt ? new Date(lastReminderAt) : now;

	switch (options.reminderFrequency || 'daily') {
		case 'immediate':
			return new Date(now.getTime() + 5 * 60 * 1000); // 5 minutes
		case 'hourly':
			return new Date(baseTime.getTime() + 60 * 60 * 1000); // 1 hour
		case 'daily':
			return new Date(baseTime.getTime() + 24 * 60 * 60 * 1000); // 24 hours
		case 'weekly':
			return new Date(baseTime.getTime() + 7 * 24 * 60 * 60 * 1000); // 7 days
		case 'custom': {
			// Handle custom interval, defaulting to 24 hours if not specified
			const hours = typeof options.customIntervalHours === 'number' && options.customIntervalHours > 0 ? options.customIntervalHours : 24;
			return new Date(baseTime.getTime() + hours * 60 * 60 * 1000);
		}
		default:
			return new Date(baseTime.getTime() + 24 * 60 * 60 * 1000);
	}
}

/**
 * Reminder System module for handling reminder-related functionality
 *
 * This module encapsulates all logic related to reminders, including
 * preferences management, reminder calculations, and reminder state
 * updates, providing a clean separation of concerns.
 */
export class ReminderSystem {
	/**
	 * Create a ReminderSystem instance
	 * @param {TaskStorage} storage - TaskStorage instance for data operations
	 */
	constructor(storage) {
		this.storage = storage;
	}

	/**
	 * Get user's reminder preferences
	 * 
	 * @param {string} userId - The ID of the user
	 * @returns {Promise<Object>} User's reminder preferences with defaults if not set
	 * @property {string} timezone - User's timezone (default: Asia/Bangkok)
	 * @property {string} preferredReminderTime - Preferred time for reminders (default: 09:00)
	 * @property {number[]} enabledDays - Days when reminders are enabled (default: Mon-Fri)
	 * @property {number} maxRemindersPerDay - Maximum reminders per day (default: 3)
	 * @property {Object} reminderTypes - Types of reminders enabled
	 * @property {Object} quietHours - Quiet hours when no reminders are sent
	 */
	async getUserReminderPreferences(userId) {
		const preferences = await this.storage.getUserReminderPreferences(userId);

		if (!preferences) {
			// Default preferences
			return {
				timezone: 'Asia/Bangkok',
				preferredReminderTime: '09:00', // 9 AM in user's timezone
				enabledDays: [1, 2, 3, 4, 5], // Monday to Friday
				maxRemindersPerDay: 3,
				reminderTypes: {
					immediate: true, // For urgent tasks
					daily: true,
					weekly: true,
					beforeDue: true,
				},
				quietHours: {
					start: '22:00',
					end: '07:00',
				},
			};
		}

		return JSON.parse(preferences);
	}

	/**
	 * Update user's reminder preferences
	 * 
	 * @param {string} userId - The ID of the user
	 * @param {Object} preferences - The new preferences to set
	 * @returns {Promise<void>}
	 */
	async updateUserReminderPreferences(userId, preferences) {
		await this.storage.updateUserReminderPreferences(userId, preferences);
		console.log(`Updated reminder preferences for user ${userId}`);
	}

	/**
	 * Get tasks that need reminders sent
	 * 
	 * @param {string} userId - The ID of the user
	 * @param {Object[]} tasks - Array of user's tasks
	 * @param {Date} [currentTime=new Date()] - The current time for comparison (used for testing)
	 * @returns {Promise<Object[]>} Array of tasks that need reminders
	 */
	async getTasksNeedingReminders(userId, tasks, currentTime = new Date()) {
		return tasks.filter((task) => {
			if (task.status !== 'pending') return false;
			if (!task.reminderSettings?.enabled) return false;
			if (task.reminderSettings?.snoozeUntil && new Date(task.reminderSettings.snoozeUntil) > currentTime) return false;

			// Check if it's time for a reminder
			const nextReminderTime = task.reminderSettings?.nextReminderAt;
			if (!nextReminderTime) return false;

			return new Date(nextReminderTime) <= currentTime;
		});
	}

	/**
	 * Update task reminder after sending
	 * 
	 * @param {string} userId - The ID of the user
	 * @param {Object} task - The task object to update
	 * @param {boolean} [reminderSent=true] - Whether the reminder was actually sent
	 * @returns {Promise<Object|null>} The updated task or null if not found
	 */
	async updateTaskReminder(userId, task, reminderSent = true) {
		const now = new Date().toISOString();

		if (reminderSent) {
			task.reminderSettings.lastReminderAt = now;
			task.reminderSettings.reminderCount += 1;
			// Use the instance method for calculating next reminder
			task.reminderSettings.nextReminderAt = this._calculateNextReminder(
				{ reminderFrequency: task.reminderSettings.frequency }, 
				now
			);
		}

		await this.storage.storeTask(userId, task);
		return task;
	}

	/**
	 * Snooze a task reminder
	 * 
	 * @param {string} userId - The ID of the user
	 * @param {Object} task - The task object to update
	 * @param {number} [snoozeMinutes=60] - Minutes to snooze the reminder (default: 60)
	 * @returns {Promise<Object|null>} The updated task or null if not found
	 */
	async snoozeTaskReminder(userId, task, snoozeMinutes = 60) {
		const snoozeUntil = new Date(Date.now() + snoozeMinutes * 60 * 1000).toISOString();

		task.reminderSettings.snoozeUntil = snoozeUntil;

		await this.storage.storeTask(userId, task);
		console.log(`Task ${task.id} snoozed until ${snoozeUntil} for user ${userId}`);

		return task;
	}

}