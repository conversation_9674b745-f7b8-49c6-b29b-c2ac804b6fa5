compatibility_flags = ["nodejs_compat"]
compatibility_date = "2025-03-21"
name = "harmony-chat"
main = "src/index.js"

vars = { DEV_MODE = "true", TIMEZONE = "Asia/Bangkok", PROTOCOL = "http", HOSTNAME = "localhost:8787", TELEGRAM_BOT_USERNAME = "hrmnyBot", TELEGRAM_BOT_NAME = "Harmony", LANGFUSE_ENVIRONMENT = "development" }

[env.dev]
vars = {}

[env.production]
vars = { DEV_MODE = "false", TIMEZONE = "Asia/Bangkok", PROTOCOL = "https", HOSTNAME = "harmony-chat-production.nandemo.workers.dev", TELEGRAM_BOT_USERNAME = "hrmnyBot", TELEGRAM_BOT_NAME = "Harmony", LANGFUSE_ENVIRONMENT = "production" }

[env.production.triggers]
crons = [
    "*/15 * * * *", # Urgent reminders - every 15 minutes
    "0 * * * *",    # High priority - every hour
    "0 */4 * * *",  # Medium priority - every 4 hours
    "0 9,17 * * *", # Low priority - twice daily (9 AM and 5 PM UTC)
    "0 9 * * *",    # Daily summary - 9 AM UTC
]

[env.production.observability]
enabled = true

[placement]
mode = "off"
