import { TaskStorage } from './storage.js';
import { TaskCreation } from './taskCreation.js';
import { ReminderSystem } from './reminderSystem.js';
import { TaskQuery } from './taskQuery.js';

/**
 * Enhanced Task Manager with improved reminder capabilities
 *
 * This class provides functionality for managing user tasks with advanced features like:
 * - Task creation with detailed metadata
 * - Reminder system with customizable frequencies
 * - Priority-based task organization
 * - Task completion and deletion
 * - User preference management for reminders
 */
export class TaskManager {
	/**
	 * Create a TaskManager instance
	 * @param {object} redisClient - Redis client instance for data persistence
	 * @throws {Error} If redisClient is not provided
	 */
	constructor(redisClient) {
		if (!redisClient) {
			throw new Error('Redis client is required for TaskManager.');
		}
		
		// Initialize component modules
		this.storage = new TaskStorage(redisClient);
		this.reminderSystem = new ReminderSystem(this.storage);
		this.taskCreation = new TaskCreation(this.storage);
		this.taskQuery = new TaskQuery(this.storage);
	}

	/**
	 * Creates a new task with enhanced metadata
	 *
	 * @param {string} userId - The ID of the user creating the task
	 * @param {string} description - The task description
	 * @param {Object} [options={}] - Optional task configuration
	 * @param {string} [options.priority='medium'] - Task priority (low, medium, high, urgent)
	 * @param {string} [options.category='general'] - Task category
	 * @param {string} [options.dueDate=null] - Due date in ISO format
	 * @param {boolean} [options.reminderEnabled=true] - Whether reminders are enabled
	 * @param {string} [options.reminderFrequency='daily'] - Reminder frequency (immediate, hourly, daily, weekly, custom)
	 * @param {number} [options.customIntervalHours=null] - Custom reminder interval in hours
	 * @param {number} [options.estimatedDuration=null] - Estimated duration in minutes
	 * @param {string[]} [options.tags=[]] - Task tags
	 * @param {string} [options.notes=null] - Additional notes
	 * @returns {Promise<Object>} The created task object
	 * @throws {Error} If userId is missing or description is invalid
	 */
	async createTask(userId, description, options = {}) {
		return this.taskCreation.createTask(userId, description, options);
	}

	/**
	 * Get user's reminder preferences
	 *
	 * @param {string} userId - The ID of the user
	 * @returns {Promise<Object>} User's reminder preferences with defaults if not set
	 * @property {string} timezone - User's timezone (default: Asia/Bangkok)
	 * @property {string} preferredReminderTime - Preferred time for reminders (default: 09:00)
	 * @property {number[]} enabledDays - Days when reminders are enabled (default: Mon-Fri)
	 * @property {number} maxRemindersPerDay - Maximum reminders per day (default: 3)
	 * @property {Object} reminderTypes - Types of reminders enabled
	 * @property {Object} quietHours - Quiet hours when no reminders are sent
	 */
	async getUserReminderPreferences(userId) {
		return this.reminderSystem.getUserReminderPreferences(userId);
	}

	/**
	 * Update user's reminder preferences
	 *
	 * @param {string} userId - The ID of the user
	 * @param {Object} preferences - The new preferences to set
	 * @returns {Promise<void>}
	 */
	async updateUserReminderPreferences(userId, preferences) {
		await this.reminderSystem.updateUserReminderPreferences(userId, preferences);
	}

	/**
	 * Get tasks that need reminders sent
	 *
	 * @param {string} userId - The ID of the user
	 * @param {Date} [currentTime=new Date()] - The current time for comparison (used for testing)
	 * @returns {Promise<Object[]>} Array of tasks that need reminders
	 */
	async getTasksNeedingReminders(userId, currentTime = new Date()) {
		const tasks = await this.getTasks(userId);
		return this.reminderSystem.getTasksNeedingReminders(userId, tasks, currentTime);
	}

	/**
	 * Update task reminder after sending
	 *
	 * @param {string} userId - The ID of the user
	 * @param {string} taskId - The ID of the task
	 * @param {boolean} [reminderSent=true] - Whether the reminder was actually sent
	 * @returns {Promise<Object|null>} The updated task or null if not found
	 */
	async updateTaskReminder(userId, taskId, reminderSent = true) {
		const key = `user:${userId}:tasks`;
		const taskData = await this.storage.storage.redis.hget(key, taskId);

		if (!taskData) {
			console.warn(`Task not found for reminder update: ${taskId} for user ${userId}`);
			return null;
		}

		const task = JSON.parse(taskData);
		return this.reminderSystem.updateTaskReminder(userId, task, reminderSent);
	}

	/**
	 * Snooze a task reminder
	 *
	 * @param {string} userId - The ID of the user
	 * @param {string} taskId - The ID of the task
	 * @param {number} [snoozeMinutes=60] - Minutes to snooze the reminder (default: 60)
	 * @returns {Promise<Object|null>} The updated task or null if not found
	 */
	async snoozeTaskReminder(userId, taskId, snoozeMinutes = 60) {
		const key = `user:${userId}:tasks`;
		const taskData = await this.storage.storage.redis.hget(key, taskId);

		if (!taskData) {
			return null;
		}

		const task = JSON.parse(taskData);
		return this.reminderSystem.snoozeTaskReminder(userId, task, snoozeMinutes);
	}

	/**
	 * Get tasks by priority level
	 *
	 * @param {string} userId - The ID of the user
	 * @param {string} priority - The priority level to filter by (low, medium, high, urgent)
	 * @returns {Promise<Object[]>} Array of pending tasks with the specified priority
	 */
	async getTasksByPriority(userId, priority) {
		return this.taskQuery.getTasksByPriority(userId, priority);
	}

	/**
	 * Get overdue tasks
	 *
	 * @param {string} userId - The ID of the user
	 * @returns {Promise<Object[]>} Array of pending tasks that are past their due date
	 */
	async getOverdueTasks(userId) {
		return this.taskQuery.getOverdueTasks(userId);
	}

	/**
	 * Get all tasks for a user
	 *
	 * @param {string} userId - The ID of the user
	 * @returns {Promise<Object[]>} Array of user's tasks
	 */
	async getTasks(userId) {
		return this.taskQuery.getTasks(userId);
	}

	/**
	 * Get all user IDs that have tasks
	 *
	 * @returns {Promise<string[]>} Array of user IDs
	 */
	async getAllUserIdsWithTasks() {
		return this.taskQuery.getAllUserIdsWithTasks();
	}

	/**
	 * Update task status
	 *
	 * @param {string} userId - The ID of the user
	 * @param {string} taskId - The ID of the task
	 * @param {string} status - The new status (pending, completed, etc.)
	 * @returns {Promise<Object|null>} The updated task or null if not found
	 */
	async updateTaskStatus(userId, taskId, status) {
		const key = `user:${userId}:tasks`;
		const taskData = await this.storage.storage.redis.hget(key, taskId);

		if (!taskData) {
			console.warn(`Task not found: ${taskId} for user ${userId}`);
			return null;
		}

		const task = typeof taskData === 'string' ? JSON.parse(taskData) : taskData;
		task.status = status;

		// If completing task, disable reminders
		if (status === 'completed') {
			task.reminderSettings.enabled = false;
			task.completedAt = new Date().toISOString();
		}

		await this.storage.storeTask(userId, task);
		console.log(`Task ${taskId} for user ${userId} updated to status: ${status}`);

		return task;
	}

	/**
	 * Complete a task by its description
	 *
	 * @param {string} userId - The ID of the user
	 * @param {string} taskDescription - Partial or full description of the task to complete
	 * @returns {Promise<Object|null>} The updated task or null if not found
	 */
	async completeTaskByDescription(userId, taskDescription) {
		const allTasks = await this.getTasks(userId);
		const pendingTasks = allTasks.filter((task) => task.status === 'pending');

		const taskToComplete = pendingTasks.find((task) => task.description.toLowerCase().includes(taskDescription.toLowerCase()));

		if (taskToComplete) {
			return this.updateTaskStatus(userId, taskToComplete.id, 'completed');
		}

		return null;
	}

	/**
	 * Delete a task by its description
	 *
	 * @param {string} userId - The ID of the user
	 * @param {string} taskDescription - Partial or full description of the task to delete
	 * @returns {Promise<boolean>} True if task was deleted, false otherwise
	 */
	async deleteTaskByDescription(userId, taskDescription) {
		const allTasks = await this.getTasks(userId);
		const pendingTasks = allTasks.filter((task) => task.status === 'pending');

		const taskToDelete = pendingTasks.find((task) => task.description.toLowerCase().includes(taskDescription.toLowerCase()));

		if (taskToDelete) {
			return this.deleteTask(userId, taskToDelete.id);
		}

		return false;
	}

	/**
	 * Delete a task by its ID
	 *
	 * @param {string} userId - The ID of the user
	 * @param {string} taskId - The ID of the task to delete
	 * @returns {Promise<boolean>} True if task was deleted, false if not found
	 */
	async deleteTask(userId, taskId) {
		const result = await this.storage.deleteTask(userId, taskId);

		if (result > 0) {
			console.log(`Task ${taskId} deleted for user ${userId}`);
			return true;
		}

		console.warn(`Task not found for deletion: ${taskId} for user ${userId}`);
		return false;
	}
}

// Export EnhancedTaskManager as an alias for backward compatibility
export { TaskManager as EnhancedTaskManager };
