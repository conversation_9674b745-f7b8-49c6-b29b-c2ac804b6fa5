import { TaskManager } from '../chat/tasks/taskManager.js';
import { getRedisClient } from '../redis/redisClient.js';
import { sendLongTelegramMessage } from '../telegram.js';
import { escapeHtml } from '../chat/telegramUtils.js';

/**
 * Enhanced Reminder Worker with intelligent scheduling and user preferences
 *
 * This worker handles scheduled task reminders with the following features:
 * - Processes reminders based on user preferences (timezone, quiet hours, enabled days)
 * - Groups tasks by priority for targeted messaging
 * - Limits reminders per day to avoid notification fatigue
 * - Sends rich, interactive messages with inline keyboards
 * - Handles errors gracefully with comprehensive logging
 * - Processes users in batches to optimize performance
 *
 * The worker is designed to run as a scheduled Cloudflare Worker, triggered by a CRON job.
 * It integrates with the TaskManager to retrieve tasks and user preferences from Redis,
 * and uses the Telegram API to send reminder messages to users.
 *
 * @example
 * // The worker is triggered automatically by a scheduled event
 * export default {
 *   async scheduled(controller, env, ctx) {
 *     ctx.waitUntil(this.processReminders(env));
 *   },
 *   // ... other methods
 * };
 */
export default {
	/**
	 * Handles the scheduled event (CRON trigger)
	 *
	 * This method is called by the Cloudflare Workers runtime when the scheduled event fires.
	 * It initiates the reminder processing workflow and ensures it runs to completion
	 * even if the request completes, using ctx.waitUntil() to keep the worker alive.
	 *
	 * @param {Object} controller - The scheduled controller object from Cloudflare Workers
	 * @param {Object} env - The environment object containing bindings (e.g., KV, D1, R2)
	 * @param {Object} ctx - The execution context for the worker
	 * @returns {Promise<void>}
	 */
	async scheduled(controller, env, ctx) {
		console.log('Running enhanced reminder worker...');
		ctx.waitUntil(this.processReminders(env));
	},

	/**
	 * Main reminder processing function
	 *
	 * Orchestrates the entire reminder workflow by:
	 * 1. Initializing the Redis client and TaskManager
	 * 2. Retrieving all user IDs with tasks
	 * 3. Processing users in batches to avoid timeouts
	 * 4. Handling errors at the top level to ensure partial completion
	 *
	 * @param {Object} env - The environment object containing bindings and configuration
	 * @returns {Promise<void>}
	 */
	async processReminders(env) {
		try {
			const redisClient = getRedisClient(env);
			const taskManager = new TaskManager(redisClient);
			const currentTime = new Date();

			const userIds = await taskManager.getAllUserIdsWithTasks();
			console.log(`Processing reminders for ${userIds.length} users at ${currentTime.toISOString()}`);

			// Process users in batches to avoid overwhelming the system
			const batchSize = 10;
			// Process users in batches to avoid timeouts and rate limits
			// This ensures we don't process too many users at once, which could
			// cause the worker to exceed its execution time limit or overwhelm
			// the Redis or Telegram APIs
			for (let i = 0; i < userIds.length; i += batchSize) {
				const batch = userIds.slice(i, i + batchSize);
				await Promise.all(batch.map((userId) => this.processUserReminders(taskManager, userId, currentTime, env)));
			}

			console.log('Enhanced reminder worker finished successfully');
		} catch (error) {
			// Log critical errors but don't re-throw to prevent worker failure
			// This ensures that partial processing is still completed even if
			// there are issues with some users or tasks
			console.error('Critical error in enhanced reminder worker:', error);
		}
	},

	/**
	 * Process reminders for a specific user
	 *
	 * Handles the complete reminder workflow for a single user, including:
	 * - Checking user preferences (quiet hours, enabled days)
	 * - Retrieving tasks that need reminders
	 * - Limiting reminders per day
	 * - Grouping tasks by priority
	 * - Sending appropriate reminder messages
	 * - Updating task reminder status
	 *
	 * @param {TaskManager} taskManager - The TaskManager instance for data access
	 * @param {string} userId - The ID of the user to process
	 * @param {Date} currentTime - The current time for comparison
	 * @param {Object} env - The environment object containing configuration and bindings
	 * @returns {Promise<void>}
	 */
	async processUserReminders(taskManager, userId, currentTime, env) {
		try {
			const preferences = await taskManager.getUserReminderPreferences(userId);

			// Check if current time is within user's quiet hours
			if (this.isQuietHours(currentTime, preferences)) {
				console.log(`Skipping reminders for user ${userId} - quiet hours`);
				return;
			}

			// Check if today is an enabled day for this user
			if (!this.isEnabledDay(currentTime, preferences)) {
				console.log(`Skipping reminders for user ${userId} - disabled day`);
				return;
			}

			const tasksNeedingReminders = await taskManager.getTasksNeedingReminders(userId, currentTime);

			if (tasksNeedingReminders.length === 0) {
				return;
			}

			// Limit reminders per day by filtering out tasks that already had a reminder today
			// This prevents sending multiple reminders for the same task in a single day
			// while still allowing reminders for new tasks that haven't been reminded about today
			const todayReminders = tasksNeedingReminders.filter((task) => {
				const lastReminder = task.reminderSettings?.lastReminderAt;
				if (!lastReminder) return true;

				const lastReminderDate = new Date(lastReminder);
				const today = new Date();
				return lastReminderDate.toDateString() !== today.toDateString();
			});

			const remindersToSend = todayReminders.slice(0, preferences.maxRemindersPerDay);

			// Group tasks by priority for better messaging
			const groupedTasks = this.groupTasksByPriority(remindersToSend);

			// Send reminders based on priority
			for (const [priority, tasks] of Object.entries(groupedTasks)) {
				if (tasks.length > 0) {
					await this.sendPriorityReminders(taskManager, userId, priority, tasks, env);
				}
			}
		} catch (error) {
			console.error(`Error processing reminders for user ${userId}:`, error);
		}
	},

	/**
	 * Check if current time is within user's quiet hours
	 *
	 * Determines whether the current time falls within the user's quiet hours,
	 * when reminders should be suppressed. Handles both regular and overnight
	 * quiet hour ranges (e.g., 22:00-07:00).
	 *
	 * @param {Date} currentTime - The current time to check
	 * @param {Object} preferences - User's reminder preferences
	 * @param {Object} preferences.quietHours - Quiet hour settings
	 * @param {string} preferences.quietHours.start - Start time in HH:MM format
	 * @param {string} preferences.quietHours.end - End time in HH:MM format
	 * @param {string} preferences.timezone - User's timezone
	 * @returns {boolean} True if current time is within quiet hours
	 */
	isQuietHours(currentTime, preferences) {
		if (!preferences.quietHours) return false;

		const userTime = this.convertToUserTimezone(currentTime, preferences.timezone);
		const currentHour = userTime.getHours();
		const currentMinute = userTime.getMinutes();
		const currentTimeMinutes = currentHour * 60 + currentMinute;

		const [startHour, startMinute] = preferences.quietHours.start.split(':').map(Number);
		const [endHour, endMinute] = preferences.quietHours.end.split(':').map(Number);

		const startTimeMinutes = startHour * 60 + startMinute;
		const endTimeMinutes = endHour * 60 + endMinute;

		// Handle overnight quiet hours (e.g., 22:00 to 07:00)
		// When start time is later than end time, it means the quiet period crosses midnight
		// So we check if current time is either after start time (same day) or before end time (next day)
		if (startTimeMinutes > endTimeMinutes) {
			return currentTimeMinutes >= startTimeMinutes || currentTimeMinutes <= endTimeMinutes;
		}

		return currentTimeMinutes >= startTimeMinutes && currentTimeMinutes <= endTimeMinutes;
	},

	/**
	 * Check if today is an enabled day for reminders
	 *
	 * Determines whether the current day of the week is enabled for sending
	 * reminders based on user preferences. Days are represented as numbers
	 * (0 = Sunday, 1 = Monday, etc.).
	 *
	 * @param {Date} currentTime - The current time to check
	 * @param {Object} preferences - User's reminder preferences
	 * @param {number[]} preferences.enabledDays - Array of enabled days (0-6)
	 * @returns {boolean} True if today is an enabled day for reminders
	 */
	isEnabledDay(currentTime, preferences) {
		const dayOfWeek = currentTime.getDay(); // 0 = Sunday, 1 = Monday, etc.
		return preferences.enabledDays.includes(dayOfWeek);
	},

	/**
	 * Convert time to user's timezone
	 *
	 * Converts a Date object to the user's local time based on their timezone
	 * preference. If the timezone is invalid, it falls back to UTC.
	 *
	 * @param {Date} time - The time to convert
	 * @param {string} timezone - The user's timezone (e.g., 'Asia/Bangkok')
	 * @returns {Date} The time converted to the user's timezone
	 */
	convertToUserTimezone(time, timezone) {
		try {
			return new Date(time.toLocaleString('en-US', { timeZone: timezone }));
		} catch {
			console.warn(`Invalid timezone ${timezone}, using UTC`);
			return time;
		}
	},

	/**
	 * Group tasks by priority
	 *
	 * Groups an array of tasks into a dictionary where the keys are priority levels
	 * and the values are arrays of tasks with that priority. Tasks without a priority
	 * are assigned to 'medium' by default.
	 *
	 * @param {Array} tasks - Array of task objects to group
	 * @returns {Object} Dictionary with priority levels as keys and arrays of tasks as values
	 */
	groupTasksByPriority(tasks) {
		return tasks.reduce((groups, task) => {
			const priority = task.priority || 'medium';
			if (!groups[priority]) groups[priority] = [];
			groups[priority].push(task);
			return groups;
		}, {});
	},

	/**
	 * Send reminders for tasks of a specific priority
	 *
	 * Sends reminder messages for tasks of a specific priority level to a user.
	 * Creates an appropriate message and keyboard, sends it via Telegram, and
	 * updates the reminder status for all tasks.
	 *
	 * @param {TaskManager} taskManager - The TaskManager instance for updating task status
	 * @param {string} userId - The ID of the user to send reminders to
	 * @param {string} priority - The priority level of the tasks ('urgent', 'high', 'medium', 'low')
	 * @param {Array} tasks - Array of task objects to remind about
	 * @param {Object} env - The environment object containing configuration and bindings
	 * @returns {Promise<void>}
	 */
	async sendPriorityReminders(taskManager, userId, priority, tasks, env) {
		try {
			const message = this.createReminderMessage(priority, tasks);

			await sendLongTelegramMessage(env, userId, message, {
				parseMode: 'HTML',
				reply_markup: this.createReminderKeyboard(tasks),
			});

			// Update reminder status for all tasks
			for (const task of tasks) {
				await taskManager.updateTaskReminder(userId, task.id, true);
			}

			console.log(`Sent ${priority} priority reminders to user ${userId} for ${tasks.length} tasks`);
		} catch (error) {
			console.error(`Failed to send ${priority} reminders to user ${userId}:`, error);
		}
	},

	/**
	 * Create contextual reminder message based on priority and tasks
	 *
	 * Creates a formatted message for task reminders with appropriate emoji,
	 * title, and content based on the priority level and number of tasks.
	 * Includes due dates, overdue status, and estimated duration when available.
	 * Uses HTML formatting for Telegram messages.
	 *
	 * @param {string} priority - The priority level of the tasks ('urgent', 'high', 'medium', 'low')
	 * @param {Array} tasks - Array of task objects to include in the reminder
	 * @returns {string} Formatted message string ready for Telegram
	 */
	createReminderMessage(priority, tasks) {
		const priorityEmojis = {
			urgent: '🚨',
			high: '⚡',
			medium: '🔔',
			low: '💭',
		};

		const priorityTitles = {
			urgent: 'URGENT REMINDER',
			high: 'High Priority Reminder',
			medium: 'Friendly Reminder',
			low: 'Gentle Reminder',
		};

		const emoji = priorityEmojis[priority] || '🔔';
		const title = priorityTitles[priority] || 'Reminder';

		let message = `${emoji} <b>${title}</b>\n\n`;

		if (tasks.length === 1) {
			const task = tasks[0];
			message += `${escapeHtml(task.description)}`;

			if (task.dueDate) {
				const dueDate = new Date(task.dueDate);
				const isOverdue = dueDate < new Date();
				const dueDateStr = dueDate.toLocaleDateString();

				if (isOverdue) {
					message += `\n\n⚠️ <b>Overdue since:</b> ${escapeHtml(dueDateStr)}`;
				} else {
					message += `\n\n📅 <b>Due:</b> ${escapeHtml(dueDateStr)}`;
				}
			}

			if (task.metadata?.estimatedDuration) {
				message += `\n⏱️ <b>Estimated time:</b> ${escapeHtml(task.metadata.estimatedDuration)}`;
			}
		} else {
			message += `You have ${tasks.length} ${priority} priority tasks:\n\n`;
			tasks.forEach((task, index) => {
				message += `${index + 1}. ${escapeHtml(task.description)}\n`;
			});
		}

		// Add instruction for user actions with proper HTML formatting
		message += `\n\n<i>Reply with /done [task] to mark as complete, or /snooze [task] to remind later.</i>`;

		return message;
	},

	/**
	 * Create inline keyboard for reminder actions
	 *
	 * Creates an inline keyboard for Telegram messages with appropriate actions
	 * based on the number of tasks. For a single task, provides specific actions
	 * (complete, snooze options). For multiple tasks, provides general actions
	 * (view all tasks, reminder settings).
	 *
	 * @param {Array} tasks - Array of task objects to create actions for
	 * @returns {Object} Telegram inline keyboard object
	 */
	createReminderKeyboard(tasks) {
		if (tasks.length === 1) {
			const task = tasks[0];
			return {
				inline_keyboard: [
					[
						{ text: '✅ Complete', callback_data: `complete_${task.id}` },
						{ text: '⏰ Snooze 1h', callback_data: `snooze_${task.id}_60` },
					],
					[
						{ text: '⏰ Snooze 4h', callback_data: `snooze_${task.id}_240` },
						{ text: '⏰ Snooze 1d', callback_data: `snooze_${task.id}_1440` },
					],
				],
			};
		}

		// For multiple tasks, provide general actions
		return {
			inline_keyboard: [
				[
					{ text: '📋 View All Tasks', callback_data: 'list_tasks' },
					{ text: '⚙️ Reminder Settings', callback_data: 'reminder_settings' },
				],
			],
		};
	},

	// Reminder callback handling is now delegated to callbackHandler.js to avoid duplication.
};
