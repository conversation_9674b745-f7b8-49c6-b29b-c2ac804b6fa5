/**
 * @fileoverview Processes incoming Telegram messages in the background.
 *
 * This module handles the complete message processing pipeline for the Telegram bot,
 * coordinating various components to provide AI-powered responses. The processing flow
 * includes:
 * 1. Message extraction and validation
 * 2. Command handling (for bot commands)
 * 3. Context preparation (chat history, user facts, semantic memory)
 * 4. Attachment processing
 * 5. AI response generation with dynamic configuration
 * 6. Response handling and delivery
 * 7. Error handling and notifications
 *
 * The function runs as a background task to avoid blocking the main request-response cycle,
 * providing a responsive experience for users while handling potentially time-consuming
 * AI operations.
 *
 * @module messageProcessor
 */

import { SYSTEM_PROMPT, CHAT_TEMPERATURE, CHAT_THINKING_BUDGET } from '../constants/index.js';
import { sendTelegramError, sendTypingAction } from '../telegram.js';
import { extractMessageData, shouldProcessMessage } from './messageExtractor.js';
import { prepareChatContext, prepareFactContext, handleAIResponse } from './chatContext.js';
import { processAttachments } from './attachmentProcessor.js';
import { callGenerativeAI } from './geminiAI.js';
import { handleCommand } from './commandHandler.js';
import { formatTimestamp } from './utils/formatUtils.js';
import { langfuseManager } from './gemini-ai/utils/LangfuseManager.js';
import { ExaSearchResults } from '@langchain/exa';
import Exa from 'exa-js';
import { taskCreateTool, taskListTool, taskCompleteTool, taskDeleteTool, taskReminderSettingsTool } from './tools/taskTools.js';
import { handleCallbackQuery, hasCallbackQuery, extractCallbackQuery } from './callbackHandler.js';
// import { getComplexityScore } from './complexity/scorer.js';

/**
 * Processes a Telegram message in the background to generate an AI-powered response.
 * This function orchestrates the complete message processing pipeline, from initial
 * validation through response generation and delivery.
 *
 * @param {Object} env - Environment variables containing configuration and secrets
 * @param {Object} webhookData - Raw Telegram webhook data containing the message
 * @returns {Promise<void>} - Resolves when processing is complete, rejects on critical errors
 *
 * @example
 * await processTelegramMessageInBackground(env, webhookData);
 */
export async function processTelegramMessageInBackground(env, webhookData) {
	// Handle callback queries from inline keyboards (e.g., reminder buttons)
	if (hasCallbackQuery(webhookData)) {
		const callbackQuery = extractCallbackQuery(webhookData);
		await handleCallbackQuery(env, callbackQuery);
		return; // Early return for callback queries
	}

	// Track the interval ID for the typing indicator to enable cleanup
	let typingInterval;

	/**
	 * Starts the typing indicator for a chat to show the user that the bot is processing.
	 * Sends an initial typing action and sets up an interval to send periodic typing
	 * actions every 5 seconds to maintain the indicator.
	 *
	 * @param {string|number} chatId - The Telegram chat ID to show the typing indicator for
	 */
	const startTypingIndicator = (chatId) => {
		sendTypingAction(env, chatId);
		typingInterval = setInterval(() => {
			sendTypingAction(env, chatId);
		}, 5000);
	};

	/**
	 * Stops the typing indicator by clearing the interval timer.
	 * This should be called when processing is complete or when an error occurs.
	 */
	const stopTypingIndicator = () => {
		if (typingInterval) {
			clearInterval(typingInterval);
			typingInterval = null;
		}
	};

	// Extract relevant message data from the webhook payload
	const messageData = extractMessageData(env, webhookData);
	const { chatId, text, userId, username, messageId, messageDate } = messageData;
	const botUsername = env.TELEGRAM_BOT_USERNAME || '';

	// Validate whether this message should be processed (e.g., not a command to another bot)
	if (!shouldProcessMessage(messageData, botUsername, webhookData)) {
		return;
	}

	// Start showing the typing indicator to provide user feedback
	startTypingIndicator(chatId);
	console.log(`Processing message from ${username} (${chatId}) in background: ${text}`);

	try {
		// Handle bot commands (e.g., /start, /help) before processing as regular messages
		const commandHandled = await handleCommand(text, env, messageData, botUsername);
		if (commandHandled) {
			console.log(`Command processed successfully for chat ${chatId}`);
			stopTypingIndicator();
			return; // Exit early since command was handled
		}

		// Prepare the chat context by retrieving conversation history and formatting it
		// for the AI model, including both recent messages and semantic history
		const { chatContent, previousMessages, formattedHistory, semanticHistory } = await prepareChatContext(
			env,
			chatId,
			botUsername,
			text,
			messageDate,
			messageData
		);

		// Prepare user-specific facts that should be included in the AI's context
		// These are personalized details about the user that can inform responses
		const { factsString } = await prepareFactContext(env, userId, text, previousMessages);

		// Process any attachments (images, documents, etc.) that were sent with the message
		// This extracts text from images, processes documents, and prepares them for AI analysis
		const attachmentParts = await processAttachments(env, messageData);

		// Construct an enhanced system prompt by replacing placeholders with actual values
		// This creates a customized instruction set for the AI model based on the current context
		const enhancedSystemPrompt = SYSTEM_PROMPT.replace('{USER_ID}', userId)
			.replace('{CURRENT_DATE}', formatTimestamp(Date.now() / 1000, env)) // Convert to seconds for Unix timestamp
			.replace('{USER_FACTS}', factsString)
			.replace('{MEMORY}', semanticHistory)
			.replace('{CONVERSATION_HISTORY}', formattedHistory);

		// Log the full system prompt for debugging and monitoring purposes
		console.log('SYSTEM_PROMPT:', enhancedSystemPrompt);

		// TODO: Re-enable complexity scoring
		// let complexityScore = 0;
		// if (text.length > 0) {
		// 	// Get complexity score for the user's message
		// 	complexityScore = await getComplexityScore(env, text, redisHistory);
		// 	console.log(`Complexity score for message: ${complexityScore}`);
		// }

		// Create dynamic tools for the AI to use
		const client = new Exa(env.EXASEARCH_API_KEY);
		const exaTool = new ExaSearchResults({
			client,
			searchArgs: {
				numResults: 10,
				type: 'fast',
				context: {
					maxCharacters: 25000,
				},
				text: {
					maxCharacters: 2000,
				},
				livecrawl: 'always',
			},
		});
		// Create all task management tools
		const createTaskTool = taskCreateTool(env, userId);
		const listTasksTool = taskListTool(env, userId);
		const completeTaskTool = taskCompleteTool(env, userId);
		const deleteTaskTool = taskDeleteTool(env, userId);
		const reminderSettingsTool = taskReminderSettingsTool(env, userId);

		// Configure the AI model with appropriate parameters for this request
		const config = {
			temperature: CHAT_TEMPERATURE, // Controls response randomness
			systemInstruction: enhancedSystemPrompt, // Customized instructions for the AI
			tools: [exaTool, createTaskTool, listTasksTool, completeTaskTool, deleteTaskTool, reminderSettingsTool], // Pass all available tools to the agent
			thinkingBudget: CHAT_THINKING_BUDGET, // Limits on AI processing time/cost
			traceTags: ['chat', `user-${userId}`], // Tags for monitoring and analytics
		};

		// Adjust model selection based on message complexity (currently commented out)
		// if (complexityScore >= 0.5) {
		// 	config.model = 'gemini-2.5-pro'; // Use more powerful model for complex queries
		// }

		// Prepare the message content to send to the AI, including both text and attachments
		const contents = [{ role: 'user', parts: [{ text: chatContent }, ...attachmentParts] }];

		// Call the generative AI model with the prepared context and configuration
		const aiResponse = await callGenerativeAI(env, config, contents);

		// Stop the typing indicator now that we have a response
		stopTypingIndicator();

		// Handle the AI response by sending it to the user and managing any follow-up actions
		await handleAIResponse(env, chatId, aiResponse.text, messageId, chatContent, factsString, formattedHistory, aiResponse.thoughts);

		// Clean up any resources used by the Langfuse monitoring system
		await langfuseManager.cleanup();
	} catch (backgroundError) {
		// Handle any errors that occur during message processing
		console.error('Error during background Telegram message processing:', backgroundError);

		// Create context for the error notification
		const context = { path: '/hrmny (background)', method: 'POST', chatId: chatId };

		try {
			// Attempt to send an error notification to the admin
			sendTelegramError(env, backgroundError, context);
		} catch (notificationError) {
			// If sending the error notification fails, log this failure
			console.error('Failed to send background processing error notification:', notificationError);
		} finally {
			// Ensure the typing indicator is stopped even if errors occur
			stopTypingIndicator();
		}
	}
}
