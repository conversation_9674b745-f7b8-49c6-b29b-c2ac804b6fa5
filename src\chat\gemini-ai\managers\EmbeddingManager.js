/**
 * @fileoverview Embedding generation manager with caching and error handling.
 */

import { ModelFactory } from '../factories/ModelFactory.js';
import { GeminiAIError, ApiKeyError } from '../errors/GeminiErrors.js';
import { getSubrequestManager } from '../../../utils/SubrequestManager.js';

/**
 * Manages embedding generation with caching and error handling
 */
export class EmbeddingManager {
	constructor(apiKeyManager) {
		this.apiKeyManager = apiKeyManager;
		this.embeddingsInstance = null;
		this.currentApiKey = null;
		this.embeddingCache = new Map(); // Cache for embeddings
		this.pendingEmbeddings = new Map(); // Track pending embedding requests
	}

	/**
	 * Creates or reuses an embeddings instance
	 * @param {string} apiKey - Valid Gemini API key
	 * @returns {GoogleGenerativeAIEmbeddings} Embeddings instance
	 */
	getEmbeddingsInstance(apiKey) {
		if (!this.embeddingsInstance || this.currentApiKey !== apiKey) {
			this.embeddingsInstance = ModelFactory.createEmbeddingsInstance(apiKey);
			this.currentApiKey = apiKey;
		}
		return this.embeddingsInstance;
	}

	/**
	 * Generates an embedding for the given text with enhanced error handling and API key rotation
	 * @param {object} env - Environment variables
	 * @param {string} textToEmbed - Text to generate embedding for
	 * @returns {Promise<number[]>} Embedding vector
	 * @throws {GeminiAIError} If API key is missing or text is invalid
	 */
	async generateEmbedding(env, textToEmbed) {
		const subrequestManager = getSubrequestManager();

		// Input validation
		if (!textToEmbed || typeof textToEmbed !== 'string' || textToEmbed.length === 0) {
			throw new GeminiAIError('Invalid text input for embedding: must be a non-empty string');
		}

		if (!env) {
			throw new GeminiAIError('Environment variables are required');
		}

		// Check subrequest capacity
		if (!subrequestManager.canMakeRequest()) {
			console.warn('[EmbeddingManager] Subrequest limit reached, skipping embedding generation');
			throw new GeminiAIError(`Subrequest limit reached (${subrequestManager.getCount()}/${subrequestManager.maxSubrequests})`);
		}

		// Create cache key
		const cacheKey = this._createCacheKey(textToEmbed);

		// Check cache first
		if (this.embeddingCache.has(cacheKey)) {
			console.log('[EmbeddingManager] Returning cached embedding');
			return this.embeddingCache.get(cacheKey);
		}

		// Check if this embedding is already being generated
		if (this.pendingEmbeddings.has(cacheKey)) {
			console.log('[EmbeddingManager] Waiting for pending embedding request');
			return await this.pendingEmbeddings.get(cacheKey);
		}

		// Create promise for this embedding request
		const embeddingPromise = this._generateEmbeddingInternal(env, textToEmbed);
		this.pendingEmbeddings.set(cacheKey, embeddingPromise);

		try {
			const result = await embeddingPromise;
			// Cache the result
			this.embeddingCache.set(cacheKey, result);
			return result;
		} finally {
			// Clean up pending request
			this.pendingEmbeddings.delete(cacheKey);
		}
	}

	/**
	 * Internal method to generate embedding
	 * @private
	 */
	async _generateEmbeddingInternal(env, textToEmbed) {
		// Load available API keys
		const apiKeys = await this.apiKeyManager.loadGeminiApiKeys(env);
		if (apiKeys.length === 0) {
			throw new ApiKeyError('No Gemini API keys configured in environment variables');
		}

		let lastError = null;
		let attempts = 0;
		const maxAttempts = 1; // Reduce attempts to save subrequests

		// Try only once to reduce subrequests
		while (attempts < maxAttempts) {
			try {
				const apiKey = await this.apiKeyManager.getNextGeminiApiKey(env);
				if (!apiKey) {
					throw new ApiKeyError('No Generative AI API key available for embedding');
				}

				// Create or reuse embeddings instance
				const embeddings = this.getEmbeddingsInstance(apiKey);

				// Generate embedding with enhanced context
				const result = await embeddings.embedQuery(textToEmbed);

				// Add basic validation for result
				if (!result || !Array.isArray(result) || result.length === 0) {
					throw new GeminiAIError('Empty or invalid embedding response from API');
				}

				// Success - return the result
				return result;
			} catch (error) {
				attempts++;
				lastError = error;

				// Enhanced error logging with context
				const errorContext = {
					textLength: textToEmbed?.length || 0,
					error: error.message,
					timestamp: new Date().toISOString(),
					attempt: attempts,
					maxAttempts: maxAttempts,
				};

				console.error('Embedding generation failed:', errorContext);

				// If this is a GeminiAIError (validation error), don't retry
				if (error instanceof GeminiAIError) {
					throw error;
				}

				// Update API key status for API-related errors
				if (this.currentApiKey) {
					await this.apiKeyManager.updateKeyStatus(this.currentApiKey, error);
				}

				// If we've exhausted all attempts, break the loop
				if (attempts >= maxAttempts) {
					break;
				}

				// Log retry attempt
				console.log(`Retrying embedding generation with next API key (attempt ${attempts + 1}/${maxAttempts})`);
			}
		}

		// All attempts failed
		throw new GeminiAIError(`Failed to generate embedding after ${attempts} attempts: ${lastError?.message || 'Unknown error'}`, lastError);
	}

	/**
	 * Create cache key for text
	 * @private
	 */
	_createCacheKey(text) {
		// Simple hash function for cache key
		let hash = 0;
		for (let i = 0; i < text.length; i++) {
			const char = text.charCodeAt(i);
			hash = (hash << 5) - hash + char;
			hash = hash & hash; // Convert to 32-bit integer
		}
		return `embed_${hash}_${text.length}`;
	}

	/**
	 * Clear embedding cache
	 */
	clearCache() {
		this.embeddingCache.clear();
		console.log('[EmbeddingManager] Cache cleared');
	}

	/**
	 * Get cache statistics
	 */
	getCacheStats() {
		return {
			cacheSize: this.embeddingCache.size,
			pendingRequests: this.pendingEmbeddings.size,
		};
	}
}
