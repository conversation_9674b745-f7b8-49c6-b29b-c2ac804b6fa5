/**
 * Task Query module for handling task retrieval and filtering
 * 
 * This module encapsulates all logic related to retrieving and filtering
 * tasks, providing a clean separation of concerns from the main
 * TaskManager class.
 */
export class TaskQuery {
	/**
	 * Create a TaskQuery instance
	 * @param {TaskStorage} storage - TaskStorage instance for data operations
	 */
	constructor(storage) {
		this.storage = storage;
	}

	/**
	 * Get all tasks for a user
	 * 
	 * @param {string} userId - The ID of the user
	 * @returns {Promise<Object[]>} Array of user's tasks
	 */
	async getTasks(userId) {
		const tasks = await this.storage.getUserTasks(userId);

		if (!tasks) {
			return [];
		}

		return Object.values(tasks)
			.map((task) => {
				try {
					return typeof task === 'string' ? JSON.parse(task) : task;
				} catch (error) {
					console.error(`Error parsing task data for user ${userId}:`, error);
					return null;
				}
			})
			.filter((task) => task !== null);
	}

	/**
	 * Get tasks by priority level
	 * 
	 * @param {string} userId - The ID of the user
	 * @param {string} priority - The priority level to filter by (low, medium, high, urgent)
	 * @returns {Promise<Object[]>} Array of pending tasks with the specified priority
	 */
	async getTasksByPriority(userId, priority) {
		const tasks = await this.getTasks(userId);
		return tasks.filter((task) => task.priority === priority && task.status === 'pending');
	}

	/**
	 * Get overdue tasks
	 * 
	 * @param {string} userId - The ID of the user
	 * @returns {Promise<Object[]>} Array of pending tasks that are past their due date
	 */
	async getOverdueTasks(userId) {
		const tasks = await this.getTasks(userId);
		const now = new Date();

		return tasks.filter((task) => {
			if (task.status !== 'pending') return false;
			if (!task.dueDate) return false;
			return new Date(task.dueDate) < now;
		});
	}

	/**
	 * Get all user IDs that have tasks
	 * 
	 * @returns {Promise<string[]>} Array of user IDs
	 */
	async getAllUserIdsWithTasks() {
		return await this.storage.getAllUserIdsWithTasks();
	}
}