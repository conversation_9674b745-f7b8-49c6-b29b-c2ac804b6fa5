import { DynamicTool } from '@langchain/core/tools';
import { TaskManager } from '../tasks/taskManager.js';
import { getRedisClient } from '../../redis/redisClient.js';

/**
 * Creates a tool for the AI to add a task to a user's to-do list.
 *
 * @param {object} env - The environment variables.
 * @param {string|number} userId - The ID of the user for whom the task will be created.
 * @returns {DynamicTool} A LangChain tool instance.
 */
export const taskCreateTool = (env, userId) => {
	return new DynamicTool({
		name: 'create_task',
		description:
			"Use this tool to create a new task or to-do item for the user when they express an intent to remember something or to do something in the future. For example, if they say 'I need to buy milk' or 'remind me to call the doctor'. Input should be a JSON object with a 'description' field containing the task description.",
		func: async (input) => {
			try {
				if (!userId) {
					return 'Error: User ID is not available. Cannot create task.';
				}

				// Handle different input formats that <PERSON><PERSON><PERSON><PERSON> might provide
				let description;

				if (typeof input === 'string') {
					// If input is a string, try to parse it as JSON first
					try {
						const parsed = JSON.parse(input);
						if (parsed && typeof parsed === 'object' && parsed.description) {
							description = parsed.description;
						} else {
							// If parsing succeeds but no description property, treat the entire string as the description
							description = input;
						}
					} catch {
						// If parsing fails, treat the entire string as the description
						description = input;
					}
				} else if (typeof input === 'object' && input !== null) {
					// If input is an object, extract the description property
					description = input.description;

					// If description is still undefined, check for common alternative property names
					if (!description) {
						description = input.task || input.text || input.content || input.message;
					}

					// If still no description, try to use the first string value found
					if (!description) {
						const stringValues = Object.values(input).filter((val) => typeof val === 'string' && val.trim() !== '');
						if (stringValues.length > 0) {
							description = stringValues[0];
						}
					}
				} else {
					// Fallback for unexpected input types (but reject null/undefined)
					if (input === null || input === undefined) {
						description = null;
					} else {
						description = String(input);
					}
				}

				// Validate that we have a description
				if (!description || typeof description !== 'string' || description.trim() === '') {
					console.error('Task tool: No valid description provided. Input was:', input);
					return 'Error: Task description is required and cannot be empty. Please provide a clear description of the task you want to create.';
				}

				const redisClient = getRedisClient(env);
				const taskManager = new TaskManager(redisClient);
				const task = await taskManager.createTask(userId, description);
				console.log(`Task tool: Successfully created task ${task.id} for user ${userId}. Description: "${description}"`);
				return `Successfully created task: "${description}". You should now confirm this with the user.`;
			} catch (error) {
				console.error('Error in create_task tool:', error);
				console.error('Input that caused error:', input);
				return 'Error: Failed to create the task due to an internal error.';
			}
		},
		schema: {
			type: 'object',
			properties: {
				description: {
					type: 'string',
					description:
						'The detailed description of the task to be created. This should be a clear, actionable description of what the user wants to remember or do.',
				},
			},
			required: ['description'],
		},
	});
};

/**
 * Creates a tool for the AI to list all tasks for a user.
 *
 * @param {object} env - The environment variables.
 * @param {string|number} userId - The ID of the user whose tasks to list.
 * @returns {DynamicTool} A LangChain tool instance.
 */
export const taskListTool = (env, userId) => {
	return new DynamicTool({
		name: 'list_tasks',
		description:
			'Use this tool to retrieve and display all tasks for the user. This shows both pending and completed tasks with their current status.',
		func: async () => {
			try {
				if (!userId) {
					return 'Error: User ID is not available. Cannot list tasks.';
				}

				const redisClient = getRedisClient(env);
				const taskManager = new TaskManager(redisClient);
				const tasks = await taskManager.getTasks(userId);

				if (tasks.length === 0) {
					return 'You have no tasks yet. Use the create_task tool to add some tasks to your list.';
				}

				// Group tasks by status
				const pendingTasks = tasks.filter((task) => task.status === 'pending');
				const completedTasks = tasks.filter((task) => task.status === 'completed');

				let response = `Found ${tasks.length} task(s):\n\n`;

				if (pendingTasks.length > 0) {
					response += `**Pending Tasks (${pendingTasks.length}):**\n`;
					pendingTasks.forEach((task, index) => {
						const createdDate = new Date(task.createdAt).toLocaleDateString();
						response += `${index + 1}. ${task.description} (Created: ${createdDate}, ID: ${task.id})\n`;
					});
					response += '\n';
				}

				if (completedTasks.length > 0) {
					response += `**Completed Tasks (${completedTasks.length}):**\n`;
					completedTasks.forEach((task, index) => {
						const createdDate = new Date(task.createdAt).toLocaleDateString();
						response += `${index + 1}. ✅ ${task.description} (Created: ${createdDate}, ID: ${task.id})\n`;
					});
				}

				console.log(`Listed ${tasks.length} tasks for user ${userId}`);
				return response.trim();
			} catch (error) {
				console.error('Error in list_tasks tool:', error);
				return 'Error: Failed to retrieve tasks due to an internal error.';
			}
		},
		schema: {
			type: 'object',
			properties: {},
			additionalProperties: false,
		},
	});
};

/**
 * Creates a tool for the AI to complete a task by description.
 *
 * @param {object} env - The environment variables.
 * @param {string|number} userId - The ID of the user whose task to complete.
 * @returns {DynamicTool} A LangChain tool instance.
 */
export const taskCompleteTool = (env, userId) => {
	return new DynamicTool({
		name: 'complete_task',
		description:
			'Use this tool to mark a task as completed when the user indicates they have finished it. Provide a description or partial description of the task to complete.',
		func: async (input) => {
			try {
				if (!userId) {
					return 'Error: User ID is not available. Cannot complete task.';
				}

				// Handle different input formats
				let taskDescription;
				if (typeof input === 'string') {
					try {
						const parsed = JSON.parse(input);
						taskDescription = parsed.description || parsed.task || input;
					} catch {
						taskDescription = input;
					}
				} else if (typeof input === 'object' && input !== null) {
					taskDescription = input.description || input.task || input.text;
				} else {
					taskDescription = String(input);
				}

				if (!taskDescription || typeof taskDescription !== 'string' || taskDescription.trim() === '') {
					return 'Error: Task description is required to complete a task. Please specify which task you want to mark as completed.';
				}

				const redisClient = getRedisClient(env);
				const taskManager = new TaskManager(redisClient);
				const completedTask = await taskManager.completeTaskByDescription(userId, taskDescription.trim());

				if (completedTask) {
					console.log(`Task completed by user ${userId}: ${completedTask.description}`);
					return `✅ Great job! I've marked "${completedTask.description}" as completed.`;
				} else {
					return `I couldn't find a pending task matching "${taskDescription}". Use the list_tasks tool to see your current tasks.`;
				}
			} catch (error) {
				console.error('Error in complete_task tool:', error);
				return 'Error: Failed to complete the task due to an internal error.';
			}
		},
		schema: {
			type: 'object',
			properties: {
				description: {
					type: 'string',
					description: 'The description or partial description of the task to mark as completed.',
				},
			},
			required: ['description'],
		},
	});
};

/**
 * Creates a tool for the AI to delete a task by description.
 *
 * @param {object} env - The environment variables.
 * @param {string|number} userId - The ID of the user whose task to delete.
 * @returns {DynamicTool} A LangChain tool instance.
 */
export const taskDeleteTool = (env, userId) => {
	return new DynamicTool({
		name: 'delete_task',
		description:
			'Use this tool to permanently delete a task when the user no longer needs it. Provide a description or partial description of the task to delete.',
		func: async (input) => {
			try {
				if (!userId) {
					return 'Error: User ID is not available. Cannot delete task.';
				}

				// Handle different input formats
				let taskDescription;
				if (typeof input === 'string') {
					try {
						const parsed = JSON.parse(input);
						taskDescription = parsed.description || parsed.task || input;
					} catch {
						taskDescription = input;
					}
				} else if (typeof input === 'object' && input !== null) {
					taskDescription = input.description || input.task || input.text;
				} else {
					taskDescription = String(input);
				}

				if (!taskDescription || typeof taskDescription !== 'string' || taskDescription.trim() === '') {
					return 'Error: Task description is required to delete a task. Please specify which task you want to remove.';
				}

				const redisClient = getRedisClient(env);
				const taskManager = new TaskManager(redisClient);
				const deleted = await taskManager.deleteTaskByDescription(userId, taskDescription.trim());

				if (deleted) {
					console.log(`Task deleted by user ${userId}: ${taskDescription}`);
					return `🗑️ Task matching "${taskDescription}" has been deleted from your list.`;
				} else {
					return `I couldn't find a pending task matching "${taskDescription}". Use the list_tasks tool to see your current tasks.`;
				}
			} catch (error) {
				console.error('Error in delete_task tool:', error);
				return 'Error: Failed to delete the task due to an internal error.';
			}
		},
		schema: {
			type: 'object',
			properties: {
				description: {
					type: 'string',
					description: 'The description or partial description of the task to delete.',
				},
			},
			required: ['description'],
		},
	});
};

/**
 * Creates a tool for the AI to manage user reminder preferences.
 *
 * @param {object} env - The environment variables.
 * @param {string|number} userId - The ID of the user whose preferences to manage.
 * @returns {DynamicTool} A LangChain tool instance.
 */
export const taskReminderSettingsTool = (env, userId) => {
	return new DynamicTool({
		name: 'reminder_settings',
		description:
			'Use this tool to view or update user reminder preferences including timezone, preferred times, quiet hours, and reminder frequency settings.',
		func: async (input) => {
			try {
				if (!userId) {
					return 'Error: User ID is not available. Cannot manage reminder settings.';
				}

				const redisClient = getRedisClient(env);
				const taskManager = new TaskManager(redisClient);

				// Handle different input formats
				let action, settings;
				if (typeof input === 'string') {
					try {
						const parsed = JSON.parse(input);
						action = parsed.action || 'view';
						settings = parsed.settings;
					} catch {
						action = input.toLowerCase().includes('update') || input.toLowerCase().includes('set') ? 'update' : 'view';
					}
				} else if (typeof input === 'object' && input !== null) {
					action = input.action || 'view';
					settings = input.settings;
				} else {
					action = 'view';
				}

				if (action === 'view') {
					const preferences = await taskManager.getUserReminderPreferences(userId);

					let response = '⚙️ **Your Reminder Settings:**\n\n';
					response += `🌍 **Timezone:** ${preferences.timezone}\n`;
					response += `⏰ **Preferred Time:** ${preferences.preferredReminderTime}\n`;
					response += `📅 **Enabled Days:** ${formatEnabledDays(preferences.enabledDays)}\n`;
					response += `🔢 **Max Reminders/Day:** ${preferences.maxRemindersPerDay}\n`;
					response += `🌙 **Quiet Hours:** ${preferences.quietHours.start} - ${preferences.quietHours.end}\n\n`;

					response += '**Reminder Types:**\n';
					Object.entries(preferences.reminderTypes).forEach(([type, enabled]) => {
						response += `• ${type}: ${enabled ? '✅' : '❌'}\n`;
					});

					response +=
						'\nTo update settings, tell me what you\'d like to change (e.g., "Set my timezone to America/New_York" or "Change quiet hours to 10 PM - 7 AM").';

					return response;
				} else if (action === 'update' && settings) {
					await taskManager.updateUserReminderPreferences(userId, settings);
					return '✅ Reminder settings updated successfully! Use the reminder_settings tool again to view your new settings.';
				} else {
					return 'Please specify what reminder settings you\'d like to view or update. For example: "Show my reminder settings" or provide specific settings to update.';
				}
			} catch (error) {
				console.error('Error in reminder_settings tool:', error);
				return 'Error: Failed to manage reminder settings due to an internal error.';
			}
		},
		schema: {
			type: 'object',
			properties: {
				action: {
					type: 'string',
					enum: ['view', 'update'],
					description: 'Whether to view current settings or update them.',
				},
				settings: {
					type: 'object',
					description: 'New reminder settings to apply (only needed for update action).',
				},
			},
		},
	});
};

// Helper function for formatting enabled days
function formatEnabledDays(enabledDays) {
	const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
	return enabledDays.map((day) => dayNames[day]).join(', ');
}
