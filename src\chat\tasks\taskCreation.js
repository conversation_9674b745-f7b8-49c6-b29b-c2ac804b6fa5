import { nanoid } from 'nanoid';
import { calculateNextReminder } from './reminderSystem.js';

/**
 * Task Creation module for handling task validation and construction
 *
 * This module encapsulates the logic for creating new tasks with proper
 * validation and metadata setup, separating this concern from the main
 * TaskManager class.
 */
export class TaskCreation {
	/**
	 * Create a TaskCreation instance
	 * @param {TaskStorage} storage - TaskStorage instance for data operations
	 */
	constructor(storage) {
		this.storage = storage;
	}

	/**
	 * Creates a new task with enhanced metadata
	 * 
	 * @param {string} userId - The ID of the user creating the task
	 * @param {string} description - The task description
	 * @param {Object} [options={}] - Optional task configuration
	 * @param {string} [options.priority='medium'] - Task priority (low, medium, high, urgent)
	 * @param {string} [options.category='general'] - Task category
	 * @param {string} [options.dueDate=null] - Due date in ISO format
	 * @param {boolean} [options.reminderEnabled=true] - Whether reminders are enabled
	 * @param {string} [options.reminderFrequency='daily'] - Reminder frequency (immediate, hourly, daily, weekly, custom)
	 * @param {number} [options.customIntervalHours=null] - Custom reminder interval in hours
	 * @param {number} [options.estimatedDuration=null] - Estimated duration in minutes
	 * @param {string[]} [options.tags=[]] - Task tags
	 * @param {string} [options.notes=null] - Additional notes
	 * @returns {Promise<Object>} The created task object
	 * @throws {Error} If userId is missing or description is invalid
	 */
	async createTask(userId, description, options = {}) {
		// Validate inputs
		if (!userId) {
			throw new Error('User ID is required to create a task');
		}

		if (!description || typeof description !== 'string' || description.trim() === '') {
			throw new Error('Task description is required and must be a non-empty string');
		}

		const cleanDescription = description.trim();
		const taskId = nanoid();

		// Enhanced task structure with reminder metadata
		const task = {
			id: taskId,
			description: cleanDescription,
			status: 'pending',
			priority: options.priority || 'medium', // low, medium, high, urgent
			category: options.category || 'general',
			createdAt: new Date().toISOString(),
			dueDate: options.dueDate || null,
			reminderSettings: {
				enabled: options.reminderEnabled !== false, // Default to enabled
				frequency: options.reminderFrequency || 'daily', // daily, weekly, custom
				lastReminderAt: null,
				reminderCount: 0,
				nextReminderAt: this._calculateNextReminder(options),
				snoozeUntil: null,
				customIntervalHours: options.customIntervalHours || null,
			},
			metadata: {
				estimatedDuration: options.estimatedDuration || null,
				tags: options.tags || [],
				notes: options.notes || null,
			},
		};

		// Store the task
		await this.storage.storeTask(userId, task);

		console.log(`Enhanced task created for user ${userId}: ${cleanDescription} (Priority: ${task.priority})`);
		return task;
	}

}