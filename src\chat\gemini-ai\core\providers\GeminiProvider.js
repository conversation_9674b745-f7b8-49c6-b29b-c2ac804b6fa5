/**
 * @fileoverview
 * Manages interactions with the Gemini API for text completions. This provider handles API key
 * rotation and fallback mechanisms across different Gemini models to ensure reliability and
 * maximize success rates for completion requests.
 */

import { ModelFactory } from '../../factories/ModelFactory.js';
import { MessageConverter } from '../../utils/MessageConverter.js';
import { ApiKeyError } from '../../errors/GeminiErrors.js';
import { DEFAULT_GEMINI_MODELS } from '../../config/constants.js';
import { langfuseManager } from '../../utils/LangfuseManager.js';
import { getSubrequestManager } from '../../../../utils/SubrequestManager.js';
import { getCircuitBreaker, CIRCUIT_BREAKER_CONFIGS } from '../../../../utils/CircuitBreaker.js';

/**
 * Implements a provider for Google's Gemini models, with support for API key management
 * and model fallbacks.
 */
export class GeminiProvider {
	/**
	 * Initializes a new instance of the GeminiProvider.
	 * @param {object} apiKeyManager - An instance of ApiKeyManager to handle Gemini API keys.
	 */
	constructor(apiKeyManager) {
		this.apiKeyManager = apiKeyManager;
	}

	/**
	 * Attempts to generate a text completion by trying different models and API keys.
	 * It iterates through available API keys and specified models, making completion
	 * requests until one succeeds.
	 * @param {object} env - The environment variables, used to load API keys.
	 * @param {object} config - Configuration for the completion request, including an optional model.
	 * @param {Array<object>} contents - The content or message history for the completion.
	 * @param {ErrorAggregator} errorAggregator - An object to collect errors from failed attempts.
	 * @returns {Promise<object|null>} The completion result or null if all attempts fail.
	 * @throws {ApiKeyError} If no Gemini API keys are available.
	 */
	async attemptCompletion(env, config, contents, errorAggregator) {
		const subrequestManager = getSubrequestManager();
		const langfuseHandler = langfuseManager.getHandler(env, config);

		// Check subrequest capacity before starting
		if (!subrequestManager.canMakeRequest()) {
			const error = new Error(
				`Cannot attempt completion: subrequest limit reached (${subrequestManager.getCount()}/${subrequestManager.maxSubrequests})`
			);
			errorAggregator.addError('subrequest_limit', error);
			console.warn('[GeminiProvider] Subrequest limit reached, skipping completion attempt');
			return null;
		}

		// Determine the models to try, using the specified model or falling back to defaults.
		const modelsToTry = (config.model ? [config.model] : DEFAULT_GEMINI_MODELS.split(',')).map((m) => m.trim()).filter(Boolean);

		console.log(`[GeminiProvider] Subrequest status:`, subrequestManager.getStatus());

		// Try with available API keys
		const apiKey = await this.apiKeyManager.getNextGeminiApiKey(env);
		if (!apiKey) {
			throw new ApiKeyError('No Gemini API key available.');
		}

		// Try only the first model to reduce subrequests
		const modelName = modelsToTry[0];
		if (modelName) {
			const circuitBreaker = getCircuitBreaker('gemini_api', CIRCUIT_BREAKER_CONFIGS.GEMINI_API);

			// Check if circuit breaker is open
			if (circuitBreaker.isOpen()) {
				console.warn('[GeminiProvider] Circuit breaker is open for Gemini API, skipping');
				errorAggregator.addError('gemini', new Error('Circuit breaker is open'));
				return null;
			}

			try {
				// If a completion is successful, return it immediately.
				return await circuitBreaker.execute(async () => {
					return await this._attemptModelCompletion(modelName, config, apiKey, contents, langfuseHandler);
				});
			} catch (error) {
				const context = `gemini/${modelName}`;
				console.error(`Failed chat completion with ${context}:`, error);
				errorAggregator.addError(context, error);

				// Update key status based on error
				await this.apiKeyManager.updateKeyStatus(apiKey, error);
			}
		}

		// If we get here, the first key failed for the first model
		// No need to try other keys/models since we want to reduce subrequests
		return null;
	}

	/**
	 * Attempts a single chat completion with a specific model and API key.
	 * @param {string} modelName - The name of the Gemini model to use.
	 * @param {object} config - Configuration for the model.
	 * @param {string} apiKey - The API key for the request.
	 * @param {Array<object>} contents - The message content for the completion.
	 * @param {object} langfuseHandler - The handler for logging to Langfuse.
	 * @returns {Promise<object>} The result from the model.
	 * @private
	 */
	async _attemptModelCompletion(modelName, config, apiKey, contents, langfuseHandler) {
		console.log(`Attempting chat completion with model: ${modelName}`);

		const agent = ModelFactory.createGeminiAgent(apiKey, modelName, config);
		const messages = MessageConverter.convertToLangChainMessages(contents);

		const invokeOptions = {};
		if (langfuseHandler) {
			invokeOptions.callbacks = [langfuseHandler];
		}

		const result = await agent.invoke({ messages }, invokeOptions);

		// Extract the content from the last message in the response.
		const lastMessage = result.messages[result.messages.length - 1];
		const responseContent = lastMessage.content;

		console.log(`✅ Successfully completed ${config.traceTags.join(', ')} with model: ${modelName}`);

		return {
			text: responseContent,
			thoughts: '', // Placeholder for any thoughts returned by the model
		};
	}
}
