/**
 * @fileoverview Redis batch operations manager to reduce the number of Redis calls
 */

import { getSubrequestManager } from './SubrequestManager.js';

/**
 * Manages batching of Redis operations to reduce subrequests
 */
export class RedisBatchManager {
	constructor(redis) {
		this.redis = redis;
		this.pendingOperations = [];
		this.batchTimeout = null;
		this.batchDelay = 10; // milliseconds to wait before executing batch
		this.maxBatchSize = 10; // maximum operations per batch
	}

	/**
	 * Add a Redis operation to the batch
	 * @param {string} operation - Redis operation type (get, set, del, etc.)
	 * @param {Array} args - Arguments for the operation
	 * @returns {Promise} Promise that resolves with the operation result
	 */
	addOperation(operation, ...args) {
		return new Promise((resolve, reject) => {
			this.pendingOperations.push({
				operation,
				args,
				resolve,
				reject
			});

			// Execute batch if we've reached max size
			if (this.pendingOperations.length >= this.maxBatchSize) {
				this._executeBatch();
			} else {
				// Schedule batch execution
				this._scheduleBatch();
			}
		});
	}

	/**
	 * Schedule batch execution with debouncing
	 * @private
	 */
	_scheduleBatch() {
		if (this.batchTimeout) {
			clearTimeout(this.batchTimeout);
		}

		this.batchTimeout = setTimeout(() => {
			this._executeBatch();
		}, this.batchDelay);
	}

	/**
	 * Execute all pending operations in a single pipeline
	 * @private
	 */
	async _executeBatch() {
		if (this.pendingOperations.length === 0) {
			return;
		}

		const subrequestManager = getSubrequestManager();
		
		// Check if we can make the request
		if (!subrequestManager.canMakeRequest()) {
			console.warn('[RedisBatchManager] Subrequest limit reached, rejecting batch operations');
			// Reject all pending operations
			this.pendingOperations.forEach(op => {
				op.reject(new Error('Subrequest limit reached'));
			});
			this.pendingOperations = [];
			return;
		}

		const operations = [...this.pendingOperations];
		this.pendingOperations = [];

		if (this.batchTimeout) {
			clearTimeout(this.batchTimeout);
			this.batchTimeout = null;
		}

		try {
			console.log(`[RedisBatchManager] Executing batch of ${operations.length} operations`);
			
			// Create pipeline
			const pipeline = this.redis.pipeline();
			
			// Add all operations to pipeline
			operations.forEach(({ operation, args }) => {
				pipeline[operation](...args);
			});

			// Execute pipeline
			const results = await pipeline.exec();

			// Resolve all promises with their results
			operations.forEach((op, index) => {
				const result = results[index];
				if (result && result.error) {
					op.reject(result.error);
				} else {
					op.resolve(result);
				}
			});

		} catch (error) {
			console.error('[RedisBatchManager] Batch execution failed:', error);
			// Reject all pending operations
			operations.forEach(op => {
				op.reject(error);
			});
		}
	}

	/**
	 * Force execute any pending operations immediately
	 */
	async flush() {
		if (this.pendingOperations.length > 0) {
			await this._executeBatch();
		}
	}

	/**
	 * Get batch statistics
	 */
	getStats() {
		return {
			pendingOperations: this.pendingOperations.length,
			hasBatchTimeout: !!this.batchTimeout
		};
	}
}

/**
 * Global batch managers per Redis client
 */
const batchManagers = new WeakMap();

/**
 * Get or create a batch manager for a Redis client
 * @param {Redis} redis - Redis client instance
 * @returns {RedisBatchManager} Batch manager for the client
 */
export function getBatchManager(redis) {
	if (!batchManagers.has(redis)) {
		batchManagers.set(redis, new RedisBatchManager(redis));
	}
	return batchManagers.get(redis);
}

/**
 * Wrapper for Redis operations that automatically batches them
 */
export class BatchedRedisClient {
	constructor(redis) {
		this.redis = redis;
		this.batchManager = getBatchManager(redis);
	}

	/**
	 * Batched get operation
	 */
	async get(key) {
		return this.batchManager.addOperation('get', key);
	}

	/**
	 * Batched set operation
	 */
	async set(key, value, options) {
		return this.batchManager.addOperation('set', key, value, options);
	}

	/**
	 * Batched del operation
	 */
	async del(key) {
		return this.batchManager.addOperation('del', key);
	}

	/**
	 * Batched incr operation
	 */
	async incr(key) {
		return this.batchManager.addOperation('incr', key);
	}

	/**
	 * Batched lpush operation
	 */
	async lpush(key, ...values) {
		return this.batchManager.addOperation('lpush', key, ...values);
	}

	/**
	 * Batched ltrim operation
	 */
	async ltrim(key, start, stop) {
		return this.batchManager.addOperation('ltrim', key, start, stop);
	}

	/**
	 * Batched smembers operation
	 */
	async smembers(key) {
		return this.batchManager.addOperation('smembers', key);
	}

	/**
	 * Batched sadd operation
	 */
	async sadd(key, ...members) {
		return this.batchManager.addOperation('sadd', key, ...members);
	}

	/**
	 * Direct access to original Redis client for operations that shouldn't be batched
	 */
	get direct() {
		return this.redis;
	}

	/**
	 * Create a pipeline (not batched)
	 */
	pipeline() {
		return this.redis.pipeline();
	}

	/**
	 * Flush any pending batched operations
	 */
	async flush() {
		return this.batchManager.flush();
	}

	/**
	 * Get batch statistics
	 */
	getBatchStats() {
		return this.batchManager.getStats();
	}
}
