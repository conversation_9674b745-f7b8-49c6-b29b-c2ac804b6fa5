/**
 * @fileoverview Request monitoring and optimization utilities
 */

import { getSubrequestManager } from './SubrequestManager.js';
import { getAllCircuitBreakerStatus } from './CircuitBreaker.js';

/**
 * Request monitoring and optimization manager
 */
export class RequestMonitor {
	constructor() {
		this.requestLog = [];
		this.maxLogSize = 100;
		this.startTime = Date.now();
	}

	/**
	 * Log a request with details
	 * @param {string} type - Type of request (api, redis, embedding, etc.)
	 * @param {string} service - Service name
	 * @param {string} operation - Operation performed
	 * @param {boolean} success - Whether the request was successful
	 * @param {number} duration - Request duration in ms
	 * @param {object} metadata - Additional metadata
	 */
	logRequest(type, service, operation, success, duration, metadata = {}) {
		const entry = {
			timestamp: Date.now(),
			type,
			service,
			operation,
			success,
			duration,
			metadata
		};

		this.requestLog.push(entry);

		// Keep log size manageable
		if (this.requestLog.length > this.maxLogSize) {
			this.requestLog.shift();
		}

		// Log warning for slow requests
		if (duration > 5000) { // 5 seconds
			console.warn(`[RequestMonitor] Slow request detected: ${service}/${operation} took ${duration}ms`);
		}
	}

	/**
	 * Get comprehensive monitoring report
	 */
	getReport() {
		const subrequestManager = getSubrequestManager();
		const circuitBreakers = getAllCircuitBreakerStatus();
		
		const now = Date.now();
		const uptime = now - this.startTime;
		
		// Analyze request log
		const stats = this._analyzeRequests();
		
		return {
			timestamp: now,
			uptime,
			subrequests: subrequestManager.getStatus(),
			circuitBreakers,
			requests: stats,
			recommendations: this._generateRecommendations(stats, subrequestManager)
		};
	}

	/**
	 * Analyze request patterns
	 * @private
	 */
	_analyzeRequests() {
		const recentRequests = this.requestLog.filter(r => Date.now() - r.timestamp < 60000); // Last minute
		
		const byType = {};
		const byService = {};
		let totalDuration = 0;
		let successCount = 0;
		let failureCount = 0;

		recentRequests.forEach(req => {
			// By type
			if (!byType[req.type]) {
				byType[req.type] = { count: 0, duration: 0, failures: 0 };
			}
			byType[req.type].count++;
			byType[req.type].duration += req.duration;
			if (!req.success) byType[req.type].failures++;

			// By service
			if (!byService[req.service]) {
				byService[req.service] = { count: 0, duration: 0, failures: 0 };
			}
			byService[req.service].count++;
			byService[req.service].duration += req.duration;
			if (!req.success) byService[req.service].failures++;

			totalDuration += req.duration;
			if (req.success) successCount++;
			else failureCount++;
		});

		return {
			total: recentRequests.length,
			successCount,
			failureCount,
			successRate: recentRequests.length > 0 ? successCount / recentRequests.length : 0,
			averageDuration: recentRequests.length > 0 ? totalDuration / recentRequests.length : 0,
			byType,
			byService
		};
	}

	/**
	 * Generate optimization recommendations
	 * @private
	 */
	_generateRecommendations(stats, subrequestManager) {
		const recommendations = [];

		// Subrequest usage recommendations
		const usage = subrequestManager.getCount() / subrequestManager.maxSubrequests;
		if (usage > 0.8) {
			recommendations.push({
				type: 'CRITICAL',
				message: `High subrequest usage: ${Math.round(usage * 100)}%. Consider reducing concurrent operations.`
			});
		} else if (usage > 0.6) {
			recommendations.push({
				type: 'WARNING',
				message: `Moderate subrequest usage: ${Math.round(usage * 100)}%. Monitor closely.`
			});
		}

		// Failure rate recommendations
		if (stats.successRate < 0.8) {
			recommendations.push({
				type: 'WARNING',
				message: `Low success rate: ${Math.round(stats.successRate * 100)}%. Check service health.`
			});
		}

		// Performance recommendations
		if (stats.averageDuration > 3000) {
			recommendations.push({
				type: 'WARNING',
				message: `High average response time: ${Math.round(stats.averageDuration)}ms. Consider optimization.`
			});
		}

		// Service-specific recommendations
		Object.entries(stats.byService).forEach(([service, serviceStats]) => {
			const serviceFailureRate = serviceStats.failures / serviceStats.count;
			if (serviceFailureRate > 0.3) {
				recommendations.push({
					type: 'WARNING',
					message: `High failure rate for ${service}: ${Math.round(serviceFailureRate * 100)}%`
				});
			}
		});

		return recommendations;
	}

	/**
	 * Get recent failed requests
	 */
	getRecentFailures(limit = 10) {
		return this.requestLog
			.filter(r => !r.success)
			.slice(-limit)
			.reverse();
	}

	/**
	 * Get slowest requests
	 */
	getSlowestRequests(limit = 10) {
		return [...this.requestLog]
			.sort((a, b) => b.duration - a.duration)
			.slice(0, limit);
	}

	/**
	 * Clear request log
	 */
	clearLog() {
		this.requestLog = [];
		console.log('[RequestMonitor] Request log cleared');
	}

	/**
	 * Check if system is healthy
	 */
	isHealthy() {
		const subrequestManager = getSubrequestManager();
		const stats = this._analyzeRequests();
		
		return {
			healthy: subrequestManager.getCount() < subrequestManager.maxSubrequests * 0.9 && 
					 stats.successRate > 0.7,
			subrequestUsage: subrequestManager.getCount() / subrequestManager.maxSubrequests,
			successRate: stats.successRate,
			averageResponseTime: stats.averageDuration
		};
	}
}

// Global monitor instance
let globalMonitor = null;

/**
 * Get global request monitor instance
 */
export function getRequestMonitor() {
	if (!globalMonitor) {
		globalMonitor = new RequestMonitor();
	}
	return globalMonitor;
}

/**
 * Utility function to wrap async operations with monitoring
 * @param {string} type - Request type
 * @param {string} service - Service name
 * @param {string} operation - Operation name
 * @param {Function} fn - Async function to execute
 * @param {object} metadata - Additional metadata
 */
export async function monitoredRequest(type, service, operation, fn, metadata = {}) {
	const monitor = getRequestMonitor();
	const startTime = Date.now();
	
	try {
		const result = await fn();
		const duration = Date.now() - startTime;
		monitor.logRequest(type, service, operation, true, duration, metadata);
		return result;
	} catch (error) {
		const duration = Date.now() - startTime;
		monitor.logRequest(type, service, operation, false, duration, { 
			...metadata, 
			error: error.message 
		});
		throw error;
	}
}

/**
 * Log system health status
 */
export function logSystemHealth() {
	const monitor = getRequestMonitor();
	const health = monitor.isHealthy();
	const report = monitor.getReport();
	
	console.log('[RequestMonitor] System Health:', {
		healthy: health.healthy,
		subrequestUsage: `${Math.round(health.subrequestUsage * 100)}%`,
		successRate: `${Math.round(health.successRate * 100)}%`,
		avgResponseTime: `${Math.round(health.averageResponseTime)}ms`,
		recommendations: report.recommendations.length
	});
	
	if (report.recommendations.length > 0) {
		console.warn('[RequestMonitor] Recommendations:', report.recommendations);
	}
}
