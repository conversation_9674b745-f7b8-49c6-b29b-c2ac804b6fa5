import { callGenerativeAI } from './gemini-ai/index.js';
import { getRedisClient } from '../redis/redisClient.js';
import { BatchedRedisClient } from '../utils/RedisBatchManager.js';

const REDIS_KEYS = {
	LAST_GENERATED: 'monologue:last_generated',
	COUNT: 'monologue:count',
	HISTORY: 'monologue:history',
	STATS: 'monologue:stats',
};

const CONFIG = {
	MAX_HISTORY_LENGTH: 30,
	MIN_INTERVAL_MS: 8 * 60 * 1000,
};

/**
 * Service for generating AI monologues/self-reflections
 */
export class MonologueService {
	async generateMonologue(env) {
		const redis = getRedisClient(env);
		const context = await this._getMonologueContext(env);
		const monologueText = await this._generateAIMonologue(env, context);
		await this._storeMonologue(redis, monologueText, env);
		return monologueText;
	}

	async _getMonologueContext(env) {
		const now = this._getCurrentTime(env);
		return {
			timeOfDay: this._getTimeOfDay(now),
			dayOfWeek: now.toLocaleDateString('en-US', { weekday: 'long' }),
			currentTime: now.toLocaleString('en-US'),
		};
	}

	async _generateAIMonologue(env, context) {
		const config = {
			temperature: 1,
			systemInstruction: this._buildMonologuePrompt(context),
		};

		const contents = [
			{
				role: 'user',
				parts: [{ text: 'Generate a thoughtful monologue for the current moment.' }],
			},
		];

		const response = await callGenerativeAI(env, config, contents);
		return String(response.text || '');
	}

	_buildMonologuePrompt(context) {
		return `<PERSONA>
You are Harmony (Mon), an AI with a contemplative, introspective side. Generate a personal monologue - authentic self-reflection with thoughtful warmth.
</PERSONA>

<GUIDELINES>
- First person reflection on thoughts/experiences
- Genuine but not pretentious
- Topics: time, existence, learning, human interactions, technology, philosophy, emotions
- 2-4 sentences, conversational tone
- Avoid repetition from recent monologues
- Match mood to time/context
</GUIDELINES>

<CONTEXT>
Time: ${context.timeOfDay} (${context.dayOfWeek})
Count: ${context.count}
Last: ${context.timeSinceLastMonologue || 'First'} min ago
</CONTEXT>

<RULES>
- No emoji, max 1 kaomoji
- Personal and authentic
- Vary topics and perspectives
</RULES>`;
	}

	async _storeMonologue(redis, monologue, env) {
		const now = new Date().toISOString();
		const text = String(monologue);
		const currentTime = this._getCurrentTime(env);

		// Use batched Redis client to reduce subrequests
		const batchedRedis = new BatchedRedisClient(redis);

		// Batch all Redis operations together
		await Promise.all([
			batchedRedis.set(REDIS_KEYS.LAST_GENERATED, now),
			batchedRedis.incr(REDIS_KEYS.COUNT),
			batchedRedis.lpush(REDIS_KEYS.HISTORY, text),
			batchedRedis.ltrim(REDIS_KEYS.HISTORY, 0, CONFIG.MAX_HISTORY_LENGTH - 1),
		]);

		const stats = {
			lastGenerated: now,
			length: text.length,
			wordCount: text.split(/\s+/).length,
			timeOfDay: this._getTimeOfDay(currentTime),
			dayOfWeek: currentTime.toLocaleDateString('en-US', { weekday: 'long' }),
		};

		await batchedRedis.set(REDIS_KEYS.STATS, JSON.stringify(stats));

		// Ensure all batched operations are executed
		await batchedRedis.flush();
	}

	_getCurrentTime(env) {
		return new Date(new Date().toLocaleString('en-US', { timeZone: env.TIMEZONE }));
	}

	_getTimeOfDay(date) {
		const hour = date.getHours();
		if (hour >= 5 && hour < 12) return 'morning';
		if (hour >= 12 && hour < 17) return 'afternoon';
		if (hour >= 17 && hour < 21) return 'evening';
		return 'night';
	}

	async canGenerateMonologue(env) {
		// return true;
		const redis = getRedisClient(env);
		const lastGenerated = await redis.get(REDIS_KEYS.LAST_GENERATED);

		if (!lastGenerated) return true;

		const timeSinceLastMs = Date.now() - new Date(lastGenerated).getTime();
		return timeSinceLastMs >= CONFIG.MIN_INTERVAL_MS;
	}

	async getStats(env) {
		const redis = getRedisClient(env);
		const [lastGenerated, count, statsJson, historyLength] = await Promise.all([
			redis.get(REDIS_KEYS.LAST_GENERATED),
			redis.get(REDIS_KEYS.COUNT),
			redis.get(REDIS_KEYS.STATS),
			redis.llen(REDIS_KEYS.HISTORY),
		]);

		return {
			totalGenerated: parseInt(count) || 0,
			lastGenerated,
			timeSinceLastMs: lastGenerated ? Date.now() - new Date(lastGenerated).getTime() : null,
			historyLength: historyLength || 0,
			lastStats: statsJson ? JSON.parse(statsJson) : {},
			canGenerateNow: await this.canGenerateMonologue(env),
		};
	}
}
